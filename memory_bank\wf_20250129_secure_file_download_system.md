# Workflow: <PERSON><PERSON> thống Download File An toàn và Logging
**<PERSON><PERSON><PERSON> tạo:** 29/01/2025  
**<PERSON><PERSON><PERSON> tiêu:** Thay thế function `gen_url_file_s3()` bằng hệ thống quản lý file download an toàn với logging và rate limiting

## Phân tích hiện tại

### Function `gen_url_file_s3()` hiện tại:
- **Vị trí:** `helpers/function.php` (line 84-103)
- **Chức năng:** Tạo URL trực tiếp đến file trên S3 hoặc local
- **Tham số:** `$path`, `$pathDefault = ''`, `$version = true`
- **Logic:**
  1. Kiểm tra nếu path đã là URL HTTP → return nguyên
  2. Kiểm tra file tồn tại local → return asset2()
  3. Tạo S3 URL với version parameter
  4. Fallback về pathDefault nếu có

### <PERSON><PERSON><PERSON> nơi sử dụng (tổng cộng ~30 lời gọi):

#### Controllers:
- `ApplyJobCrudController.php` (2 lần): preview file đánh giá, upload response
- `ApplyJobCvController.php` (2 lần): log file paths
- `DownloadController.php` (1 lần): download CV
- `JobCrudController.php` (1 lần): export JD file

#### Models:
- `Cv.php` (2 lần): getUrlCvPublicAttribute, getUrlCvPrivateAttribute
- `ApplyJob.php` (1 lần): getInterviewEvaluationFileUrlAttribute

#### Jobs/Commands:
- `MatchCvJob.php` (1 lần): lấy CV URL cho AI matching
- `PushCvToRecland.php` (2 lần): push CV public/private
- `PushRawCvsToRecland.php` (2 lần): push raw CV data
- `PushToReclandFromCvProcessed.php` (2 lần): push processed CV
- `FTPBackupFileS3.php` (1 lần): backup file
- `UploadHideCv.php` (1 lần): hide CV service

#### Services:
- `CvService.php` (1 lần): AI hide CV service

#### Views:
- `cvs/edit.blade.php` (1 lần): iframe preview
- `apply_job/show.blade.php` (2 lần): CV private paths
- `jobs/show.blade.php` (1 lần): JD file link
- `candidates/detail-view.blade.php` (8 lần): attachment links/images

#### Resources:
- `CvResource.php` (1 lần): API response

## Kế hoạch thực hiện

### 1. ✅ Phân tích codebase hiện tại
- [x] Tìm hiểu function gen_url_file_s3()
- [x] Liệt kê tất cả nơi sử dụng
- [x] Phân loại theo mức độ ưu tiên thay thế

### 2. ✅ Tạo migration và model cho logging
- [x] Tạo migration file_access_logs
- [x] Tạo model FileAccessLog với relationships và scopes
- [x] Test migration thành công

### 3. ✅ Tạo Controller Action mới
- [x] Tạo FileDownloadController
- [x] Implement action download với validation
- [x] Thêm route cho controller
- [x] Test route hoạt động

### 4. ✅ Tạo Helper Function thay thế
- [x] Tạo function secure_file_url()
- [x] Implement hash/checksum security
- [x] Test function mới hoạt động

### 5. ✅ Implement Rate Limiting & Security
- [x] Tạo FileSecurityService
- [x] Thêm rate limiting logic
- [x] Tạo email alert system
- [x] Implement suspicious activity detection
- [x] IP blocking mechanism

### 6. 🔄 Migration Plan - Đã thay thế các file quan trọng
- [x] **Models (Ưu tiên cao):**
  - [x] Cv.php: getUrlCvPublicAttribute, getUrlCvPrivateAttribute
  - [x] ApplyJob.php: getInterviewEvaluationFileUrlAttribute

- [x] **Controllers quan trọng:**
  - [x] ApplyJobCrudController.php: upload response, preview file
  - [x] DownloadController.php: CV download (sử dụng signed URL trực tiếp)

- [x] **Views quan trọng:**
  - [x] cvs/edit.blade.php: iframe preview
  - [x] jobs/show.blade.php: JD file link
  - [x] apply_job/show.blade.php: CV private links (partial)

- [ ] **Còn lại cần thay thế:**
  - [ ] ApplyJobCvController.php (2 lần)
  - [ ] JobCrudController.php (1 lần - export)
  - [ ] CvResource.php (1 lần - API)
  - [ ] candidates/detail-view.blade.php (8 lần - attachments)
  - [ ] Jobs/Commands (7 lần - background tasks)
  - [ ] Services (1 lần - CvService)

## Ghi chú kỹ thuật
- Sử dụng Laravel's Storage facade cho S3
- Implement signed URLs với expiration
- Rate limiting: 100 requests/10 minutes
- Email alert: <EMAIL>
- Hash algorithm: SHA256 với app key