{"__meta": {"id": "Xfcc2d880815ebfe0583dd940952ccf9a", "datetime": "2025-07-29 10:48:46", "utime": **********.555378, "method": "GET", "uri": "/admin/apply-job/1890/show", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753760925.865372, "end": **********.555394, "duration": 0.6900219917297363, "duration_str": "690ms", "measures": [{"label": "Booting", "start": 1753760925.865372, "relative_start": 0, "end": **********.180033, "relative_end": **********.180033, "duration": 0.31466102600097656, "duration_str": "315ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.180044, "relative_start": 0.31467199325561523, "end": **********.555396, "relative_end": 2.1457672119140625e-06, "duration": 0.375352144241333, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 39376872, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 107, "templates": [{"name": "1x admins.apply_job.show", "param_count": null, "params": [], "start": **********.368813, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.phpadmins.apply_job.show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "admins.apply_job.show"}, {"name": "1x backpack.theme-tabler::blank", "param_count": null, "params": [], "start": **********.394927, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/blank.blade.phpbackpack.theme-tabler::blank", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::blank"}, {"name": "4x backpack.theme-tabler::inc.widgets", "param_count": null, "params": [], "start": **********.395789, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/widgets.blade.phpbackpack.theme-tabler::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.theme-tabler::inc.widgets"}, {"name": "1x backpack.ui::widgets.style", "param_count": null, "params": [], "start": **********.397485, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/widgets/style.blade.phpbackpack.ui::widgets.style", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Fwidgets%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::widgets.style"}, {"name": "9x __components::723fe3f0e44fe2b3529303522562360e", "param_count": null, "params": [], "start": **********.399083, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/723fe3f0e44fe2b3529303522562360e.blade.php__components::723fe3f0e44fe2b3529303522562360e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F723fe3f0e44fe2b3529303522562360e.blade.php&line=1", "ajax": false, "filename": "723fe3f0e44fe2b3529303522562360e.blade.php", "line": "?"}, "render_count": 9, "name_original": "__components::723fe3f0e44fe2b3529303522562360e"}, {"name": "1x backpack.theme-tabler::layouts.horizontal_dark", "param_count": null, "params": [], "start": **********.400337, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/horizontal_dark.blade.phpbackpack.theme-tabler::layouts.horizontal_dark", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fhorizontal_dark.blade.php&line=1", "ajax": false, "filename": "horizontal_dark.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.horizontal_dark"}, {"name": "1x backpack.theme-tabler::inc.head", "param_count": null, "params": [], "start": **********.401126, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/head.blade.phpbackpack.theme-tabler::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.head"}, {"name": "1x backpack.theme-tabler::inc.theme_styles", "param_count": null, "params": [], "start": **********.402157, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_styles.blade.phpbackpack.theme-tabler::inc.theme_styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_styles.blade.php&line=1", "ajax": false, "filename": "theme_styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_styles"}, {"name": "1x backpack.ui::inc.styles", "param_count": null, "params": [], "start": **********.406171, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/styles.blade.phpbackpack.ui::inc.styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.styles"}, {"name": "1x backpack.theme-tabler::layouts.partials.light_dark_mode_logic", "param_count": null, "params": [], "start": **********.413396, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/light_dark_mode_logic.blade.phpbackpack.theme-tabler::layouts.partials.light_dark_mode_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Flight_dark_mode_logic.blade.php&line=1", "ajax": false, "filename": "light_dark_mode_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.light_dark_mode_logic"}, {"name": "1x backpack.theme-tabler::layouts._horizontal_dark.menu_container", "param_count": null, "params": [], "start": **********.414381, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal_dark/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal_dark.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal_dark%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal_dark.menu_container"}, {"name": "1x backpack.theme-tabler::layouts._horizontal.menu_container", "param_count": null, "params": [], "start": **********.415081, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal.menu_container"}, {"name": "2x backpack.theme-tabler::inc.sidebar_content", "param_count": null, "params": [], "start": **********.416023, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/sidebar_content.blade.phpbackpack.theme-tabler::inc.sidebar_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fsidebar_content.blade.php&line=1", "ajax": false, "filename": "sidebar_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.sidebar_content"}, {"name": "2x backpack.ui::inc.menu_items", "param_count": null, "params": [], "start": **********.417399, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.phpbackpack.ui::inc.menu_items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fui%2Finc%2Fmenu_items.blade.php&line=1", "ajax": false, "filename": "menu_items.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.ui::inc.menu_items"}, {"name": "44x backpack.theme-tabler::components.menu-dropdown-item", "param_count": null, "params": [], "start": **********.422787, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown-item.blade.phpbackpack.theme-tabler::components.menu-dropdown-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown-item.blade.php&line=1", "ajax": false, "filename": "menu-dropdown-item.blade.php", "line": "?"}, "render_count": 44, "name_original": "backpack.theme-tabler::components.menu-dropdown-item"}, {"name": "12x backpack.theme-tabler::components.menu-dropdown", "param_count": null, "params": [], "start": **********.426432, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown.blade.phpbackpack.theme-tabler::components.menu-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown.blade.php&line=1", "ajax": false, "filename": "menu-dropdown.blade.php", "line": "?"}, "render_count": 12, "name_original": "backpack.theme-tabler::components.menu-dropdown"}, {"name": "1x backpack.theme-tabler::inc.menu", "param_count": null, "params": [], "start": **********.449911, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu.blade.phpbackpack.theme-tabler::inc.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu"}, {"name": "2x backpack.theme-tabler::inc.topbar_left_content", "param_count": null, "params": [], "start": **********.450644, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_left_content.blade.phpbackpack.theme-tabler::inc.topbar_left_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_left_content.blade.php&line=1", "ajax": false, "filename": "topbar_left_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_left_content"}, {"name": "2x backpack.theme-tabler::layouts.partials.switch_theme", "param_count": null, "params": [], "start": **********.451382, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/switch_theme.blade.phpbackpack.theme-tabler::layouts.partials.switch_theme", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fswitch_theme.blade.php&line=1", "ajax": false, "filename": "switch_theme.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::layouts.partials.switch_theme"}, {"name": "1x backpack.theme-tabler::inc.menu_notification_dropdown", "param_count": null, "params": [], "start": **********.454878, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu_notification_dropdown.blade.phpbackpack.theme-tabler::inc.menu_notification_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu_notification_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_notification_dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu_notification_dropdown"}, {"name": "2x backpack.theme-tabler::inc.topbar_right_content", "param_count": null, "params": [], "start": **********.497098, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_right_content.blade.phpbackpack.theme-tabler::inc.topbar_right_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_right_content.blade.php&line=1", "ajax": false, "filename": "topbar_right_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_right_content"}, {"name": "2x backpack.theme-tabler::inc.menu_user_dropdown", "param_count": null, "params": [], "start": **********.497757, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/menu_user_dropdown.blade.phpbackpack.theme-tabler::inc.menu_user_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fmenu_user_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_user_dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.menu_user_dropdown"}, {"name": "1x backpack.theme-tabler::layouts.partials.mobile_toggle_btn", "param_count": null, "params": [], "start": **********.5043, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/mobile_toggle_btn.blade.phpbackpack.theme-tabler::layouts.partials.mobile_toggle_btn", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fmobile_toggle_btn.blade.php&line=1", "ajax": false, "filename": "mobile_toggle_btn.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.mobile_toggle_btn"}, {"name": "1x backpack.theme-tabler::inc.breadcrumbs", "param_count": null, "params": [], "start": **********.541611, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/breadcrumbs.blade.phpbackpack.theme-tabler::inc.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.breadcrumbs"}, {"name": "1x backpack.theme-tabler::inc.footer", "param_count": null, "params": [], "start": **********.542451, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/footer.blade.phpbackpack.theme-tabler::inc.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.footer"}, {"name": "1x backpack.ui::inc.scripts", "param_count": null, "params": [], "start": **********.544062, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/scripts.blade.phpbackpack.ui::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.scripts"}, {"name": "7x __components::8a07812e6d8f9d2826754abad88e5380", "param_count": null, "params": [], "start": **********.545108, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/8a07812e6d8f9d2826754abad88e5380.blade.php__components::8a07812e6d8f9d2826754abad88e5380", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F8a07812e6d8f9d2826754abad88e5380.blade.php&line=1", "ajax": false, "filename": "8a07812e6d8f9d2826754abad88e5380.blade.php", "line": "?"}, "render_count": 7, "name_original": "__components::8a07812e6d8f9d2826754abad88e5380"}, {"name": "1x backpack.theme-tabler::inc.alerts", "param_count": null, "params": [], "start": **********.547214, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/alerts.blade.phpbackpack.theme-tabler::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": **********.548726, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}, {"name": "1x backpack.theme-tabler::inc.theme_scripts", "param_count": null, "params": [], "start": **********.550482, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_scripts.blade.phpbackpack.theme-tabler::inc.theme_scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_scripts.blade.php&line=1", "ajax": false, "filename": "theme_scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_scripts"}]}, "route": {"uri": "GET admin/apply-job/{id}/show", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin, Closure", "as": "apply-job.show", "operation": "show", "controller": "App\\Http\\Controllers\\Admin\\ApplyJobCrudController@show", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FShowOperation.php&line=71\" onclick=\"\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ShowOperation.php:71-90</a>"}, "queries": {"nb_statements": 16, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.060719999999999996, "accumulated_duration_str": "60.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.233696, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.24756, "duration": 0.024149999999999998, "duration_str": "24.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 39.773}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.3459752, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 39.773, "width_percent": 1.103}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.348815, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 40.876, "width_percent": 0.988}, {"sql": "select * from `apply_jobs` where `apply_jobs`.`id` = '1890' limit 1", "type": "query", "params": [], "bindings": ["1890"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 50}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 380}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}], "start": **********.3563402, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Read.php:76", "source": {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=76", "ajax": false, "filename": "Read.php", "line": "76"}, "connection": "c_hri", "explain": null, "start_percent": 41.864, "width_percent": 0.758}, {"sql": "select * from `jobs` where `jobs`.`id` = 298 and `jobs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 165}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.3599641, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:381", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=381", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "381"}, "connection": "c_hri", "explain": null, "start_percent": 42.622, "width_percent": 0.823}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.374361, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 43.445, "width_percent": 0.939}, {"sql": "select * from `career_levels` where `career_levels`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 94}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3771, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:94", "source": {"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=94", "ajax": false, "filename": "show.blade.php", "line": "94"}, "connection": "c_hri", "explain": null, "start_percent": 44.384, "width_percent": 0.675}, {"sql": "select `skills`.*, `cv_skills`.`cv_id` as `pivot_cv_id`, `cv_skills`.`skill_id` as `pivot_skill_id` from `skills` inner join `cv_skills` on `skills`.`id` = `cv_skills`.`skill_id` where `cv_skills`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 107}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.379022, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:107", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=107", "ajax": false, "filename": "show.blade.php", "line": "107"}, "connection": "c_hri", "explain": null, "start_percent": 45.059, "width_percent": 0.955}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 117}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.381309, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:117", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=117", "ajax": false, "filename": "show.blade.php", "line": "117"}, "connection": "c_hri", "explain": null, "start_percent": 46.014, "width_percent": 0.906}, {"sql": "select * from `meta` where `meta`.`owner_type` = 'App\\\\Models\\\\ApplyJob' and `meta`.`owner_id` = 1890 and `meta`.`owner_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\ApplyJob", 1890], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 118}, {"index": 22, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/GetMeta.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\GetMeta.php", "line": 24}, {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.386336, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 46.92, "width_percent": 1.383}, {"sql": "select * from `status` where `status`.`id` = 81 limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 229}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.391006, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 48.304, "width_percent": 0.741}, {"sql": "select * from `status` where `status`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 229}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.392494, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:229", "source": {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=229", "ajax": false, "filename": "show.blade.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 49.045, "width_percent": 0.642}, {"sql": "select * from `notifications` where `user_id` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.4529939, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ClientViewComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FViewComposers%2FClientViewComposer.php&line=19", "ajax": false, "filename": "ClientViewComposer.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 49.687, "width_percent": 0.939}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.458029, "duration": 0.02214, "duration_str": "22.14ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 50.626, "width_percent": 36.462}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.498193, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 87.088, "width_percent": 6.472}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.506013, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 93.561, "width_percent": 6.439}]}, "models": {"data": {"App\\Models\\Notification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FNotification.php&line=1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\Status": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FStatus.php&line=1", "ajax": false, "filename": "Status.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ApplyJob": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\Job": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Cv": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}, "App\\Models\\CareerLevel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLevel.php&line=1", "ajax": false, "filename": "CareerLevel.php", "line": "?"}}, "App\\Models\\Skill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FSkill.php&line=1", "ajax": false, "filename": "Skill.php", "line": "?"}}, "App\\Models\\CareerLanguage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLanguage.php&line=1", "ajax": false, "filename": "CareerLanguage.php", "line": "?"}}}, "count": 20, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 45, "messages": [{"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-441676900 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441676900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353753, "xdebug_link": null}, {"message": "[\n  ability => apply-job.edit,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1656248997 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656248997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354888, "xdebug_link": null}, {"message": "[\n  ability => apply-job.show,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-574132665 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.show </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.show</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574132665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355505, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-260697814 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260697814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421821, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-79755665 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79755665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424037, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1045673387 data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045673387\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424987, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1221705996 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221705996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425931, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1209182210 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209182210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42829, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1316400021 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316400021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.429132, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1822443557 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822443557\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.429914, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-885535759 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885535759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431264, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-892541578 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892541578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432126, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1806105937 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806105937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432945, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1010492440 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010492440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433978, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.434874, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437217, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1783077510 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783077510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.438355, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2049689560 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049689560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439083, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-637449452 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637449452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.440055, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-736896629 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736896629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.44099, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-17930503 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17930503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.443825, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1105173972 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105173972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.44549, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1721758734 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721758734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.447543, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-853998597 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853998597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.44912, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1482698126 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482698126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.515572, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1016449175 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016449175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.516897, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.518114, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1779742195 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779742195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.519078, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949978509 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949978509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.521238, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1495798712 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495798712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522103, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2072035312 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072035312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522891, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-328918297 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328918297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.52425, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1094399696 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094399696\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.525066, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-144126973 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144126973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.525876, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403587715 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403587715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.5267, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527547, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1851357435 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851357435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529535, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1517203598 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517203598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530629, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2049560574 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049560574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53132, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-743861351 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743861351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532279, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1175577272 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175577272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.533217, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1341243418 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341243418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535459, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701112447 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701112447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536883, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-182515005 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182515005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.539033, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1775527251 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775527251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.540687, "xdebug_link": null}]}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job/1890/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/apply-job/1890/show", "status_code": "<pre class=sf-dump id=sf-dump-1539735569 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1539735569\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-968688813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-968688813\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-760949882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-760949882\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1803157353 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InQzVTRoK1pMdmRWRkRId1N5R2RFUmc9PSIsInZhbHVlIjoiaTRnbWpxMU11dWtyQUF3NnFkYlVhTFhielM3U2NJMnkzK25hRXMyY1B2czNWamlmalNicUJRVGllK3FSQmdUUUJJU1FYUVhnNndQdkQwMjJPbEZkWTJac1lwelQ5UnFHYjZ1Y21tajVEYVhtdCtuV3luclhFTlZGS2hlWTVHV2ciLCJtYWMiOiI0MTU4ZDY1NDQ1NTZkZTQ4NWE4ZDBjMDFjMDlhZjIyZjYxNjcxMTZlOTc1ZDViMjRmNDgwZWFjNTAyYTcwZWYyIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IllHNmR6YnJSSGRIYXBxRHZlSUtCL0E9PSIsInZhbHVlIjoiRjhScC9oeVJTOWdYR0tVTmxmei84MXBDclV0dnlFaDFycm1tTit0MVdzMGdTaXNQVWlVTnNhZFREdXk3aVl1eW9RMThlMGcrQWxIdUhLK3YwSlhKWXpqWEdLS0FUb0kyMWp4SUlBb0s1R3k0RjFmWkRpVVAySkpPN2MyelFyK3IiLCJtYWMiOiJmODFlNGM5NTNmMzcwM2Y3M2QxN2VkMTcxMGMzM2I1NmFhYzY4M2IxMDk3YTRmMGJkNGIzZThmYzBjOWEzMGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803157353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1430951534 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430951534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1233031296 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:48:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpCYlNJWlFGRWZLTDdnZTFrTmlnMFE9PSIsInZhbHVlIjoib2NFZTlkcmNJd3dSR1hONjhTV29iVW8zNE5NNHBGMW8rK21rcncxVk8yRjlrQmVPeHd1bnFPVjRoK0xOc2FRbUNpMTc0M2xscnJZZzBIN3Q4dFV5Z0Z2ckxvTUhWVWMzMk1TVngwaFIvblhwaFN3UGI0VjZDcGJUbWFadWhyVG0iLCJtYWMiOiJiY2ZmYWFlYjliY2NlN2YxNjIwZjE0ZjhjMmU0Nzg1NDE1MTVmN2U4MzcxN2M0MWZhZDkxZDUyNTRmMWY1YWYxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:48:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6InFOK3YyV0FjZ0lIMElmZS9OVnZCWHc9PSIsInZhbHVlIjoidnpvRnJiRWNhejl1QUdPcTA5RmJvMGlZSWxLa0JUNXhCcjd2WWQrQWN0Tk1JMVFhZE5zcFp3Szl1a3JST2lyYmdURFBUNFdaUWVHVlZOQ3F2SEVJMUxPTWczd0did1hldlQydXArenhnTS90ZzVCSjlRT2g1Uk1wQlEzS0RQV0UiLCJtYWMiOiJhMmU5MDdmOTk2OGJhZTg5MzAzZDRmZWI2NWJmYzg4YjRiYTAxZDZmZDdiMGViZWJjMDcyM2UyMGFhOWQzMDI2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:48:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpCYlNJWlFGRWZLTDdnZTFrTmlnMFE9PSIsInZhbHVlIjoib2NFZTlkcmNJd3dSR1hONjhTV29iVW8zNE5NNHBGMW8rK21rcncxVk8yRjlrQmVPeHd1bnFPVjRoK0xOc2FRbUNpMTc0M2xscnJZZzBIN3Q4dFV5Z0Z2ckxvTUhWVWMzMk1TVngwaFIvblhwaFN3UGI0VjZDcGJUbWFadWhyVG0iLCJtYWMiOiJiY2ZmYWFlYjliY2NlN2YxNjIwZjE0ZjhjMmU0Nzg1NDE1MTVmN2U4MzcxN2M0MWZhZDkxZDUyNTRmMWY1YWYxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:48:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6InFOK3YyV0FjZ0lIMElmZS9OVnZCWHc9PSIsInZhbHVlIjoidnpvRnJiRWNhejl1QUdPcTA5RmJvMGlZSWxLa0JUNXhCcjd2WWQrQWN0Tk1JMVFhZE5zcFp3Szl1a3JST2lyYmdURFBUNFdaUWVHVlZOQ3F2SEVJMUxPTWczd0did1hldlQydXArenhnTS90ZzVCSjlRT2g1Uk1wQlEzS0RQV0UiLCJtYWMiOiJhMmU5MDdmOTk2OGJhZTg5MzAzZDRmZWI2NWJmYzg4YjRiYTAxZDZmZDdiMGViZWJjMDcyM2UyMGFhOWQzMDI2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:48:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233031296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1212543871 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212543871\", {\"maxDepth\":0})</script>\n"}}