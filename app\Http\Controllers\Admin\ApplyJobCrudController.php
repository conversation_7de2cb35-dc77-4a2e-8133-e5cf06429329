<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Requests\ApplyJobRequest;
use App\Models\ApplyJob;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\Cv;
use App\Models\Job;
use App\Models\Status;
use App\Services\ApplyJobService;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Http\Request;
use App\Services\FileServiceS3;

/**
 * Class ApplyJobCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ApplyJobCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;


    protected $statusService;


    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return voiduploadInterviewEvaluation
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\ApplyJob::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/apply-job');
        CRUD::setEntityNameStrings('apply job', 'apply jobs');
        $this->crud->denyAccess(['update', 'delete']);
        CRUD::enableExportButtons();
        // CRUD::addClause('outSide');
        if (!backpack_user()->can('apply-job.index')) {
            CRUD::denyAccess('list');
        }
        if (!backpack_user()->can('apply-job.edit')) {
            CRUD::denyAccess('update');
        }
        if (!backpack_user()->can('apply-job.show')) {
            CRUD::denyAccess('show');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {

        CRUD::disableResponsiveTable();
        if (request()->has('log_status') && request()->has('user_id') && request()->has('startDate')) {
            $endDate = request()->has('endDate') && !empty(request()->get('endDate')) ? request()->get('endDate') : date('Y-m-d');
            $this->crud->addClause('whereHas', 'logChangeStatusApply', function ($query) use ($endDate) {
                $query->where('status_id', request()->get('log_status'));
                $query->where('date_status', '>=', request()->get('startDate'));
                $query->where('date_status', '<=', $endDate . ' 23:59:59');
            });
            $this->crud->addClause('where', 'apply_jobs.created_by', request()->get('user_id'));
        }
        //        $this->crud->removeButton('edit');
        $this->filterData();
        CRUD::column('row_number')->type('row_number')->label('#')->orderable(false);
        //        CRUD::column('candidate_id')->label('Ứng viên');

        CRUD::addColumn([
            'name' => 'job',
            'label' => 'Company',
            'type' => 'text ',
            'value' => function ($entry) {
                if (!empty($entry->job)) {
                    if (!empty($entry->job->company)) {
                        return $entry->job->company->company_abbreviation;
                    }
                }
                return '';
            }
        ]);

        CRUD::addColumn([
            'name' => 'job_title',
            'label' => 'Job Title',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if ($entry->job) {
                    return '<div class="fw-bold mb-2"><a href="' . route('job.show', $entry->job->id) . '" target="_blank">' . $entry->job->title . '</a></div>';
                }
                return '';
            },
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhereHas('job', function ($q) use ($column, $searchTerm) {
                    $q->where('title', 'like', '%' . $searchTerm . '%');
                });
            }
        ]);

        CRUD::addColumn([
            'name' => 'statusApply',
            'label' => 'Status',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if ($entry->statusApply) {
                    $statusParent = $entry->statusApply->where('id', $entry->statusApply->parent_id)->first();
                    if ($statusParent) {
                        return '<div class="fw-bold  mb-2">' . ($statusParent ? $statusParent->name : null) . '</div><div class="fw-lighter">' . $entry->statusApply->name . '</div>';
                    }
                }
                return '';
            },
            'visibleInExport' => false
        ]);

        CRUD::addColumn([
            'name' => 'candidate_cv',
            'label' => 'Ứng viên',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->candidate)) {
                    return '<div class="mb-2">' . $entry->candidate->name . '</div>';
                } else {
                    return '';
                }
            },
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhereHas('candidate', function ($q) use ($column, $searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            }
        ]);

        CRUD::addColumn([
            'name' => 'candidate',
            'label' => 'Trường học/Ngôn ngữ',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->cv)) {
                    return '<div class="fw-bold mb-2">' . $entry->university . '</div><div class="fw-lighter">' . implode(', ', $entry->cv->careerLanguages->pluck('name_vi')->toArray()) . '</div>';
                } else {
                    return '';
                }
            }
        ]);


        CRUD::addColumn([
            'name' => 'desired_salary',
            'label' => 'Mức lương mong muốn',
            'type' => 'custom_html',
            'value' => function ($entry) {
                return number_format($entry->desired_salary);
            }
        ]);

        CRUD::addColumn([
            'name' => 'candidate_strengths',
            'label' => 'Ưu điểm của ứng cử viên',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->candidate_strengths)) {
                    $strengths = nl2br(htmlspecialchars($entry->candidate_strengths));
                    return '<div class="mb-2" style="width: 400px; text-wrap: pretty;">' . $strengths . '</div>';
                }
                return '';
            }
        ]);


        CRUD::column('date_onboard')->label('Ngày bắt đầu onboard');


        CRUD::addColumn([
            'name' => 'cv',
            'label' => 'CV private',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if (!empty($entry->cv->url_cv_private)) {
                    return '<a href="' . $entry->cv->url_cv_private . '" target="_blank" class="btn btn-sm btn-link"><span><i class="la la-eye"></i> Xem Cv</span></a>';
                }
                return '';
            },
            'visibleInExport' => false
        ]);

        CRUD::addColumn([
            'name' => 'createdBy',
            'label' => 'Người tạo',
            'type' => 'custom_html',
            'value' => function ($entry) {
                $user = $entry->createdBy;
                $user_name = Utils::getUsernameFromEmail($user->email);
                return $user_name;
            }
        ]);

        // Cột trạng thái cho export
        CRUD::addColumn([
            'name' => 'status_export',
            'label' => 'Trạng thái',
            'type' => 'closure',
            'function' => function ($entry) {
                if ($entry->statusApply) {
                    $statusParent = $entry->statusApply->where('id', $entry->statusApply->parent_id)->first();
                    if ($statusParent) {
                        return $statusParent->name . ' - ' . $entry->statusApply->name;
                    }
                    return $entry->statusApply->name;
                }
                return '';
            },
            'exportOnlyColumn' => true,
            'visibleInTable' => false,
        ]);

        // Cột ghi chú trạng thái cuối
        CRUD::addColumn([
            'name' => 'last_status_note',
            'label' => 'Ghi chú của trạng thái cuối',
            'type' => 'closure',
            'function' => function ($entry) {
                $latestLog = \App\Models\LogChangeStatusApply::where('apply_job_id', $entry->id)
                    ->latest('id')
                    ->first();
                return $latestLog ? $latestLog->note : '';
            },
            'exportOnlyColumn' => true,
            'visibleInTable' => false,
        ]);

        // Cột ngày tạo (ngày ứng tuyển)
        CRUD::addColumn([
            'name' => 'created_at',
            'label' => 'Ngày ứng tuyển',
            'type' => 'date',
            'format' => 'Y-m-d H:i:s',
            'exportOnlyColumn' => true,
            'visibleInTable' => false,
        ]);
        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(ApplyJobRequest::class);
        CRUD::addFields($this->fieldData());

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function storeAjax(ApplyJobRequest $request)
    {

        $data = $request->only(['cv_id', 'job_id', 'candidate_id', 'note', 'candidate_strengths', 'date_onboard', 'desired_salary', 'university']);

        if (!isset($data['status']) || empty($data['status'])) {
            $default_status = Status::where('group', 'apply-job')->where('name', 'Đã ứng tuyển')->first();
            if ($default_status) {
                $data['status'] = $default_status->id;
            } else {
                $data['status'] = null;
            }
        }

        return response()->json((new ApplyJobService())->create($data));
    }

    protected function fieldData()
    {
        return [
            [
                'type' => "relationship",
                'name' => 'job',
                'ajax' => true,
                'placeholder' => 'Choose option',
                'minimum_input_length' => 0,
                'wrapper' => [
                    'class' => 'form-group',
                ],
            ],
            [
                'type' => "relationship",
                'name' => 'candidate',
                'ajax' => true,
                'placeholder' => 'Choose option',
                'minimum_input_length' => 0,
                'wrapper' => [
                    'class' => 'form-group',
                ],

            ],

            [  // Select2
                'label' => 'Trạng thái',
                'type' => 'select2',
                'name' => 'status',     // the db column for the foreign key

                // optional
                'entity' => 'statusApply',     // the method that defines the relationship in your Model
                'model' => Status::class,    // foreign key model
                'attribute' => 'name',   // foreign key attribute that is shown to user
                'validationRules' => 'required',
                'wrapper' => [
                    'class' => 'form-group',
                ],
                'default' => null,
                'placeholder' => 'Choose option',
                'allows_clear' => true,
                'options' => (function ($query) {
                    return $query->whereGroup('apply-job')->where('is_active', 1)->whereNotNull('parent_id')->select('id', 'name')->get();
                }),
            ],
        ];
    }

    public function fetchJob()
    {

        return $this->fetch(\App\Models\Job::class);
    }

    public function fetchCandidate()
    {

        return $this->fetch(\App\Models\Candidate::class);
    }

    protected function setupShowOperation()
    {
        $this->crud->setShowView('admins.apply_job.show');
        $user = backpack_user();
        $entry = $this->crud->getCurrentEntry();
        $job = Job::find($entry->job_id);
        if (!$user->canShowDetailJob($job->id)) {
            abort(403, 'Không có quyền truy cập');
        }
        // if (!$user->canOutsideAccess() && $entry->created_by != $user->id) {
        //     // abort(403, 'Không có quyền truy cập');
        // }
        Widget::add([
            'type' => 'style',
            'content' => 'assets/css/admin/custom.css',
        ]);
    }

    public function filterData()
    {
        $this->crud->addFilter(
            [
                'name' => 'company',
                'type' => 'select2_multiple',
                'label' => 'Company'
            ],
            function () {
                return Company::pluck('company_abbreviation', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('job.company', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'createdBy',
                'type' => 'select2_multiple',
                'label' => 'Sale đăng job'
            ],
            function () {
                $user_ids = Job::distinct('created_by')->pluck('created_by')->toArray();
                $users = \App\Models\User::whereIn('id', $user_ids)->pluck('email', 'id')->toArray();
                $rs = [];
                # map user email to username
                $users = array_map(function ($email) {
                    return Utils::getUsernameFromEmail($email);
                }, $users);
                return $users;
            },
            function ($value) {
                // $this->crud->query->whereHas('job', function ($query) use ($value) {
                //     $query->whereIn('created_by', json_decode($value));
                // });
                $this->crud->query->select('apply_jobs.*')->join('jobs', 'jobs.id', '=', 'apply_jobs.job_id')
                    ->whereIn('jobs.created_by', json_decode($value));
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'ApplycreatedBy',
                'type' => 'select2_multiple',
                'label' => 'Người Apply'
            ],
            function () {
                $user_ids = ApplyJob::distinct('created_by')->pluck('created_by')->toArray();
                $users = \App\Models\User::whereIn('id', $user_ids)->pluck('email', 'id')->toArray();
                $rs = [];
                # map user email to username
                $users = array_map(function ($email) {
                    return Utils::getUsernameFromEmail($email);
                }, $users);
                return $users;
            },
            function ($value) {
                // $this->crud->query->whereHas('job', function ($query) use ($value) {
                //     $query->whereIn('created_by', json_decode($value));
                // });
                $this->crud->query->whereIn('apply_jobs.created_by', json_decode($value));
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'job_id',
                'type' => 'select2_multiple',
                'label' => 'Job'
            ],
            function () {
                return Job::pluck('title', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('job', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'status',
                'type' => 'select2_multiple',
                'label' => 'Status'
            ],
            function () {
                return Status::where('group', 'apply-job')->whereNull('parent_id')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('statusApply', function ($query) use ($value) {
                    $query->whereIn('parent_id', json_decode($value));
                });
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'job_type',
                'type' => 'select2',
                'label' => 'Job Type'
            ],
            function () {
                return Status::where('group', 'job-type')->whereNull('parent_id')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('job', function ($query) use ($value) {
                    // dd($value);
                    $query->where('type', $value);
                });
            }
        );

        CRUD::filter('from_to')
            ->type('date_range')
            ->label('Khoảng thời gian ứng tuyển ')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                CRUD::addClause('where', 'apply_jobs.updated_at', '>=', $dates->from);
                CRUD::addClause('where', 'apply_jobs.updated_at', '<=', $dates->to . ' 23:59:59');
            });
    }

    /**
     * Upload file đánh giá kết quả phỏng vấn
     */
    public function uploadInterviewEvaluation(Request $request, $id)
    {
        $request->validate([
            'interview_evaluation_file' => 'required|file|mimes:pdf,doc,docx,xlsx,xls,ppt,pptx',
        ]);

        $applyJob = ApplyJob::findOrFail($id);

        // Kiểm tra quyền truy cập
        $user = backpack_user();
        $job = Job::find($applyJob->job_id);
        if (!$user->canShowDetailJob($job->id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Không có quyền truy cập'
            ], 403);
        }

        // Upload file lên AWS S3
        $filePath = null;
        if ($request->hasFile('interview_evaluation_file')) {
            $filePath = \App\Services\FileServiceS3::getInstance()->uploadToS3(
                $request->file('interview_evaluation_file'),
                PATH_FOLDER_INTERVIEW_EVALUATION
            );
        }

        if ($filePath) {
            // Cập nhật đường dẫn file vào database
            $applyJob->update(['interview_evaluation_file' => $filePath]);

            return response()->json([
                'status' => 'success',
                'message' => 'Upload file đánh giá thành công',
                'data' => [
                    'file_path' => $filePath,
                    'file_url' => secure_file_url($applyJob->id, 'ApplyJob', 'interview_evaluation_file')
                ]
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Có lỗi xảy ra khi upload file'
        ], 500);
    }

    /**
     * Preview file đánh giá kết quả phỏng vấn
     */
    public function previewInterviewEvaluation($id)
    {
        $applyJob = ApplyJob::findOrFail($id);

        // Kiểm tra quyền truy cập
        $user = backpack_user();
        $job = Job::find($applyJob->job_id);
        if (!$user->canShowDetailJob($job->id)) {
            abort(403, 'Không có quyền truy cập');
        }

        if (empty($applyJob->interview_evaluation_file)) {
            abort(404, 'File đánh giá không tồn tại');
        }

        // Redirect đến URL file trên S3
        $fileUrl = secure_file_url($applyJob->id, 'ApplyJob', 'interview_evaluation_file');
        if (strpos($fileUrl, '.pdf') !== false) {
            $iframeSrc = $fileUrl;
        } else {
            $iframeSrc = "https://view.officeapps.live.com/op/embed.aspx?src=" . urlencode($fileUrl);
        }
        return redirect($iframeSrc);
    }
}
