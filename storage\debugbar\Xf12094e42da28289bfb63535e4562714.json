{"__meta": {"id": "Xf12094e42da28289bfb63535e4562714", "datetime": "2025-07-29 10:45:10", "utime": **********.719655, "method": "POST", "uri": "/api/status-apply-job", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.246137, "end": **********.719675, "duration": 0.4735381603240967, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.246137, "relative_start": 0, "end": **********.552052, "relative_end": **********.552052, "duration": 0.30591511726379395, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.552067, "relative_start": 0.30593013763427734, "end": **********.719677, "relative_end": 1.9073486328125e-06, "duration": 0.16760993003845215, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30943456, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/status-apply-job", "middleware": "api, web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Api\\Admin\\StatusController@getAllStatusApplyJob", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FApi%2FAdmin%2FStatusController.php&line=19\" onclick=\"\">app/Http/Controllers/Api/Admin/StatusController.php:19-23</a>"}, "queries": {"nb_statements": 34, "nb_visible_statements": 35, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01677, "accumulated_duration_str": "16.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.621289, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.634964, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 11.986}, {"sql": "select * from `status` where `status`.`parent_id` = 23 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.669131, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 11.986, "width_percent": 2.027}, {"sql": "select * from `status` where `status`.`parent_id` = 81 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.67148, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 14.013, "width_percent": 1.849}, {"sql": "select * from `status` where `status`.`parent_id` = 25 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.672756, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 15.862, "width_percent": 3.28}, {"sql": "select * from `status` where `status`.`parent_id` = 26 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.674238, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 19.141, "width_percent": 3.16}, {"sql": "select * from `status` where `status`.`parent_id` = 27 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6756968, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 22.302, "width_percent": 3.339}, {"sql": "select * from `status` where `status`.`parent_id` = 56 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.677177, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 25.641, "width_percent": 2.564}, {"sql": "select * from `status` where `status`.`parent_id` = 57 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.678544, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 28.205, "width_percent": 3.101}, {"sql": "select * from `status` where `status`.`parent_id` = 58 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.67998, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 31.306, "width_percent": 3.041}, {"sql": "select * from `status` where `status`.`parent_id` = 29 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.681511, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 34.347, "width_percent": 3.459}, {"sql": "select * from `status` where `status`.`parent_id` = 30 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.683016, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 37.806, "width_percent": 3.28}, {"sql": "select * from `status` where `status`.`parent_id` = 31 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6844862, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 41.085, "width_percent": 2.922}, {"sql": "select * from `status` where `status`.`parent_id` = 59 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.685914, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 44.007, "width_percent": 2.982}, {"sql": "select * from `status` where `status`.`parent_id` = 97 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [97], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.68735, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 46.989, "width_percent": 2.862}, {"sql": "select * from `status` where `status`.`parent_id` = 98 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6887531, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 49.851, "width_percent": 2.862}, {"sql": "select * from `status` where `status`.`parent_id` = 33 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6902242, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 52.713, "width_percent": 2.862}, {"sql": "select * from `status` where `status`.`parent_id` = 34 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.691647, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 55.575, "width_percent": 2.982}, {"sql": "select * from `status` where `status`.`parent_id` = 60 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.693078, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 58.557, "width_percent": 3.041}, {"sql": "select * from `status` where `status`.`parent_id` = 36 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.694588, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 61.598, "width_percent": 3.339}, {"sql": "select * from `status` where `status`.`parent_id` = 37 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.696072, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 64.937, "width_percent": 2.862}, {"sql": "select * from `status` where `status`.`parent_id` = 38 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.697477, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 67.8, "width_percent": 2.922}, {"sql": "select * from `status` where `status`.`parent_id` = 39 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.698957, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 70.722, "width_percent": 3.28}, {"sql": "select * from `status` where `status`.`parent_id` = 41 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.700512, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 74.001, "width_percent": 1.968}, {"sql": "select * from `status` where `status`.`parent_id` = 42 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.701755, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 75.969, "width_percent": 1.908}, {"sql": "select * from `status` where `status`.`parent_id` = 43 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.70298, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 77.877, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 44 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.704209, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 79.666, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 46 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.705478, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 81.455, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 47 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.706726, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 83.244, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 48 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.707936, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 85.033, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 62 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.709187, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 86.822, "width_percent": 1.789}, {"sql": "select * from `status` where `status`.`parent_id` = 63 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.710398, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 88.611, "width_percent": 1.908}, {"sql": "select * from `status` where `status`.`parent_id` = 65 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.711681, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 90.519, "width_percent": 3.518}, {"sql": "select * from `status` where `status`.`parent_id` = 66 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.713218, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 94.037, "width_percent": 2.445}, {"sql": "select * from `status` where `status`.`parent_id` = 99 and `status`.`parent_id` is not null", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/Api/Admin/StatusResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Resources\\Api\\Admin\\StatusResource.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.714582, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "c_hri", "explain": null, "start_percent": 96.482, "width_percent": 3.518}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job/1890/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/api/status-apply-job", "status_code": "<pre class=sf-dump id=sf-dump-1406060471 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1406060471\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1267487537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1267487537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-72678712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-72678712\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-984790156 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InpWSzd4cFBaWjFWK01vcnJhSG1tVkE9PSIsInZhbHVlIjoiTkMzTFRra0s0UklCTEJVN1c1cG9NVEtxWVUvVGxFU3hRTitBUmJoUjlLRUdlaWZ3ckgvcGd4bldUa1pIclNvMklqUzdza0lTek5veVM2bnZUVVhHSCtYTktqNE84V2NkRkViK3JuajdPZWNOU1NtQ3hDTHZBbEk0a09oTzlsK3QiLCJtYWMiOiIzN2U2NmU4ZjAwNDFlNTcwNjE0YzU1OWIxNWJlNTlkNDRjNTFkY2MwMjMxMzlhMzFmYmIwZmJkZTFhNGY4OWQ1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">http://chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpWSzd4cFBaWjFWK01vcnJhSG1tVkE9PSIsInZhbHVlIjoiTkMzTFRra0s0UklCTEJVN1c1cG9NVEtxWVUvVGxFU3hRTitBUmJoUjlLRUdlaWZ3ckgvcGd4bldUa1pIclNvMklqUzdza0lTek5veVM2bnZUVVhHSCtYTktqNE84V2NkRkViK3JuajdPZWNOU1NtQ3hDTHZBbEk0a09oTzlsK3QiLCJtYWMiOiIzN2U2NmU4ZjAwNDFlNTcwNjE0YzU1OWIxNWJlNTlkNDRjNTFkY2MwMjMxMzlhMzFmYmIwZmJkZTFhNGY4OWQ1IiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IlpOVldoaldxT1dsdFUxUnllODBVUXc9PSIsInZhbHVlIjoiV1Y5NDVibFo2b3phZFN3OEZkY251VDJIMjAzaXZFQTREMnZNU0F3eXNUZHFZZXhQaHlEdUZjN1dsazYyRG05cEdweFAvMUE1ajZjaU0rOHRnZVVNQStkVGg0azRUdVNObDdEWnJiYjRIbkc0WE5hWVU4ZWhQbWV1U1J0bEhNSUUiLCJtYWMiOiI1MjNmZjk0MTNkY2Y4ODQ3YzRmMTRjM2MxNGY0NjM3NTVjNzdhNzdhMTUwMTA4ZjM2ZWExNjZjNzAxY2E1NGI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984790156\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-740724207 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740724207\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1304141032 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:45:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZwcWxuT1VRK2JFYmJONzlpWGxZbHc9PSIsInZhbHVlIjoidm9oTkVwZ0RDeTNQSTBkbG95Y1RtekViOVpUVU9uNXRNN1Vsc3AvZVFuQlZ3ajRnd1prUjl1Z2hnd0h2Mis1OGRIZDdjUGxjU2N6cHpxbGNOd0VmT25HRUlkbldVMHNIaGVpd3E1U29rM1U2b3NPMi8wYVRmRW82cGxiMW4yUTEiLCJtYWMiOiI3YzJhMzQ2MmEwZWVhZDMwOWZmYWI1MDNkYzA4MDhiZGU3NDQ5OGViOGQ3OGNlZTU4Y2RmYzQ2NWQ3NjgwNDdlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:45:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6ImRoK3crUld6NC95ZlZmaE9kTktleFE9PSIsInZhbHVlIjoiOWdMaDljdTI1bHdFUU5CenpwR3NjWjUvOWU3eGxqNkN2TVpkMVdRZHh3WHBzQWVhR1BzTkM5YjNGOEk5U1Nrc2V4VWNwOXF5cnNCcmlPQVhxUXlMMG4zVEpYejB0WDRwVmF0Y1A1bVZFQmw0S3FTR0p4RjgrUU94Ny9QV0tLaEIiLCJtYWMiOiJjNjJhYWI3OTI3OTI2OTU2YjYyNGNhMDY5MjNiODRjMTYxZjI3YmViOWRhNWQ1YTUwZDgxNGQ4MTU1NDkyNDEwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:45:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZwcWxuT1VRK2JFYmJONzlpWGxZbHc9PSIsInZhbHVlIjoidm9oTkVwZ0RDeTNQSTBkbG95Y1RtekViOVpUVU9uNXRNN1Vsc3AvZVFuQlZ3ajRnd1prUjl1Z2hnd0h2Mis1OGRIZDdjUGxjU2N6cHpxbGNOd0VmT25HRUlkbldVMHNIaGVpd3E1U29rM1U2b3NPMi8wYVRmRW82cGxiMW4yUTEiLCJtYWMiOiI3YzJhMzQ2MmEwZWVhZDMwOWZmYWI1MDNkYzA4MDhiZGU3NDQ5OGViOGQ3OGNlZTU4Y2RmYzQ2NWQ3NjgwNDdlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:45:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6ImRoK3crUld6NC95ZlZmaE9kTktleFE9PSIsInZhbHVlIjoiOWdMaDljdTI1bHdFUU5CenpwR3NjWjUvOWU3eGxqNkN2TVpkMVdRZHh3WHBzQWVhR1BzTkM5YjNGOEk5U1Nrc2V4VWNwOXF5cnNCcmlPQVhxUXlMMG4zVEpYejB0WDRwVmF0Y1A1bVZFQmw0S3FTR0p4RjgrUU94Ny9QV0tLaEIiLCJtYWMiOiJjNjJhYWI3OTI3OTI2OTU2YjYyNGNhMDY5MjNiODRjMTYxZjI3YmViOWRhNWQ1YTUwZDgxNGQ4MTU1NDkyNDEwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:45:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304141032\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-653165059 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653165059\", {\"maxDepth\":0})</script>\n"}}