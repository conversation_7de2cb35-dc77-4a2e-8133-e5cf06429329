<?php

namespace App\Listeners;

use App\Events\ApplyJobCreatedEvent;
use App\Events\ApplyJobStatusChangedEvent;
use App\Events\LogTaskCreatedEvent;
use App\Events\TaskCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ApplyJobStatusChangedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ApplyJobStatusChangedEvent $event): void
    {
        $apply_job_log = $event->apply_job_log;
        $apply_job_log->notify(new \App\Notifications\ApplyJobStatusChangedNotification());
    }
}
