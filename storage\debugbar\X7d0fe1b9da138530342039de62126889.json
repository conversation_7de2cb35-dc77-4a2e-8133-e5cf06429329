{"__meta": {"id": "X7d0fe1b9da138530342039de62126889", "datetime": "2025-07-29 10:11:17", "utime": 1753758677.220546, "method": "GET", "uri": "/admin/apply-job/1890/show", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484421, "end": 1753758677.220563, "duration": 0.7361419200897217, "duration_str": "736ms", "measures": [{"label": "Booting", "start": **********.484421, "relative_start": 0, "end": **********.784067, "relative_end": **********.784067, "duration": 0.29964590072631836, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.784078, "relative_start": 0.29965686798095703, "end": 1753758677.220565, "relative_end": 2.1457672119140625e-06, "duration": 0.43648719787597656, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52205992, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 107, "templates": [{"name": "1x admins.apply_job.show", "param_count": null, "params": [], "start": **********.918535, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.phpadmins.apply_job.show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "admins.apply_job.show"}, {"name": "1x backpack.theme-tabler::blank", "param_count": null, "params": [], "start": 1753758677.057281, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/blank.blade.phpbackpack.theme-tabler::blank", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::blank"}, {"name": "4x backpack.theme-tabler::inc.widgets", "param_count": null, "params": [], "start": 1753758677.058278, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/widgets.blade.phpbackpack.theme-tabler::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.theme-tabler::inc.widgets"}, {"name": "1x backpack.ui::widgets.style", "param_count": null, "params": [], "start": 1753758677.060134, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/widgets/style.blade.phpbackpack.ui::widgets.style", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Fwidgets%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::widgets.style"}, {"name": "9x __components::723fe3f0e44fe2b3529303522562360e", "param_count": null, "params": [], "start": 1753758677.06177, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/723fe3f0e44fe2b3529303522562360e.blade.php__components::723fe3f0e44fe2b3529303522562360e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F723fe3f0e44fe2b3529303522562360e.blade.php&line=1", "ajax": false, "filename": "723fe3f0e44fe2b3529303522562360e.blade.php", "line": "?"}, "render_count": 9, "name_original": "__components::723fe3f0e44fe2b3529303522562360e"}, {"name": "1x backpack.theme-tabler::layouts.horizontal_dark", "param_count": null, "params": [], "start": 1753758677.063163, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/horizontal_dark.blade.phpbackpack.theme-tabler::layouts.horizontal_dark", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fhorizontal_dark.blade.php&line=1", "ajax": false, "filename": "horizontal_dark.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.horizontal_dark"}, {"name": "1x backpack.theme-tabler::inc.head", "param_count": null, "params": [], "start": 1753758677.063977, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/head.blade.phpbackpack.theme-tabler::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.head"}, {"name": "1x backpack.theme-tabler::inc.theme_styles", "param_count": null, "params": [], "start": 1753758677.065, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_styles.blade.phpbackpack.theme-tabler::inc.theme_styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_styles.blade.php&line=1", "ajax": false, "filename": "theme_styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_styles"}, {"name": "1x backpack.ui::inc.styles", "param_count": null, "params": [], "start": 1753758677.069192, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/styles.blade.phpbackpack.ui::inc.styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.styles"}, {"name": "1x backpack.theme-tabler::layouts.partials.light_dark_mode_logic", "param_count": null, "params": [], "start": 1753758677.076537, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/light_dark_mode_logic.blade.phpbackpack.theme-tabler::layouts.partials.light_dark_mode_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Flight_dark_mode_logic.blade.php&line=1", "ajax": false, "filename": "light_dark_mode_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.light_dark_mode_logic"}, {"name": "1x backpack.theme-tabler::layouts._horizontal_dark.menu_container", "param_count": null, "params": [], "start": 1753758677.077601, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal_dark/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal_dark.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal_dark%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal_dark.menu_container"}, {"name": "1x backpack.theme-tabler::layouts._horizontal.menu_container", "param_count": null, "params": [], "start": 1753758677.07836, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal.menu_container"}, {"name": "2x backpack.theme-tabler::inc.sidebar_content", "param_count": null, "params": [], "start": 1753758677.079287, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/sidebar_content.blade.phpbackpack.theme-tabler::inc.sidebar_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fsidebar_content.blade.php&line=1", "ajax": false, "filename": "sidebar_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.sidebar_content"}, {"name": "2x backpack.ui::inc.menu_items", "param_count": null, "params": [], "start": 1753758677.080829, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.phpbackpack.ui::inc.menu_items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fui%2Finc%2Fmenu_items.blade.php&line=1", "ajax": false, "filename": "menu_items.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.ui::inc.menu_items"}, {"name": "44x backpack.theme-tabler::components.menu-dropdown-item", "param_count": null, "params": [], "start": 1753758677.086068, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown-item.blade.phpbackpack.theme-tabler::components.menu-dropdown-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown-item.blade.php&line=1", "ajax": false, "filename": "menu-dropdown-item.blade.php", "line": "?"}, "render_count": 44, "name_original": "backpack.theme-tabler::components.menu-dropdown-item"}, {"name": "12x backpack.theme-tabler::components.menu-dropdown", "param_count": null, "params": [], "start": 1753758677.089541, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown.blade.phpbackpack.theme-tabler::components.menu-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown.blade.php&line=1", "ajax": false, "filename": "menu-dropdown.blade.php", "line": "?"}, "render_count": 12, "name_original": "backpack.theme-tabler::components.menu-dropdown"}, {"name": "1x backpack.theme-tabler::inc.menu", "param_count": null, "params": [], "start": 1753758677.11257, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu.blade.phpbackpack.theme-tabler::inc.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu"}, {"name": "2x backpack.theme-tabler::inc.topbar_left_content", "param_count": null, "params": [], "start": 1753758677.113371, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_left_content.blade.phpbackpack.theme-tabler::inc.topbar_left_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_left_content.blade.php&line=1", "ajax": false, "filename": "topbar_left_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_left_content"}, {"name": "2x backpack.theme-tabler::layouts.partials.switch_theme", "param_count": null, "params": [], "start": 1753758677.114068, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/switch_theme.blade.phpbackpack.theme-tabler::layouts.partials.switch_theme", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fswitch_theme.blade.php&line=1", "ajax": false, "filename": "switch_theme.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::layouts.partials.switch_theme"}, {"name": "1x backpack.theme-tabler::inc.menu_notification_dropdown", "param_count": null, "params": [], "start": 1753758677.118011, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu_notification_dropdown.blade.phpbackpack.theme-tabler::inc.menu_notification_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu_notification_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_notification_dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu_notification_dropdown"}, {"name": "2x backpack.theme-tabler::inc.topbar_right_content", "param_count": null, "params": [], "start": 1753758677.158425, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_right_content.blade.phpbackpack.theme-tabler::inc.topbar_right_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_right_content.blade.php&line=1", "ajax": false, "filename": "topbar_right_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_right_content"}, {"name": "2x backpack.theme-tabler::inc.menu_user_dropdown", "param_count": null, "params": [], "start": 1753758677.159095, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/menu_user_dropdown.blade.phpbackpack.theme-tabler::inc.menu_user_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fmenu_user_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_user_dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.menu_user_dropdown"}, {"name": "1x backpack.theme-tabler::layouts.partials.mobile_toggle_btn", "param_count": null, "params": [], "start": 1753758677.167692, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/mobile_toggle_btn.blade.phpbackpack.theme-tabler::layouts.partials.mobile_toggle_btn", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fmobile_toggle_btn.blade.php&line=1", "ajax": false, "filename": "mobile_toggle_btn.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.mobile_toggle_btn"}, {"name": "1x backpack.theme-tabler::inc.breadcrumbs", "param_count": null, "params": [], "start": 1753758677.2057, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/breadcrumbs.blade.phpbackpack.theme-tabler::inc.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.breadcrumbs"}, {"name": "1x backpack.theme-tabler::inc.footer", "param_count": null, "params": [], "start": 1753758677.206521, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/footer.blade.phpbackpack.theme-tabler::inc.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.footer"}, {"name": "1x backpack.ui::inc.scripts", "param_count": null, "params": [], "start": 1753758677.208199, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/scripts.blade.phpbackpack.ui::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.scripts"}, {"name": "7x __components::8a07812e6d8f9d2826754abad88e5380", "param_count": null, "params": [], "start": 1753758677.20928, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/8a07812e6d8f9d2826754abad88e5380.blade.php__components::8a07812e6d8f9d2826754abad88e5380", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F8a07812e6d8f9d2826754abad88e5380.blade.php&line=1", "ajax": false, "filename": "8a07812e6d8f9d2826754abad88e5380.blade.php", "line": "?"}, "render_count": 7, "name_original": "__components::8a07812e6d8f9d2826754abad88e5380"}, {"name": "1x backpack.theme-tabler::inc.alerts", "param_count": null, "params": [], "start": 1753758677.211493, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/alerts.blade.phpbackpack.theme-tabler::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": 1753758677.213263, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}, {"name": "1x backpack.theme-tabler::inc.theme_scripts", "param_count": null, "params": [], "start": 1753758677.215049, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_scripts.blade.phpbackpack.theme-tabler::inc.theme_scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_scripts.blade.php&line=1", "ajax": false, "filename": "theme_scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_scripts"}]}, "route": {"uri": "GET admin/apply-job/{id}/show", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin, Closure", "as": "apply-job.show", "operation": "show", "controller": "App\\Http\\Controllers\\Admin\\ApplyJobCrudController@show", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FShowOperation.php&line=71\" onclick=\"\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ShowOperation.php:71-90</a>"}, "queries": {"nb_statements": 24, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.053849999999999995, "accumulated_duration_str": "53.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.838001, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.8518162, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 4.457}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.893775, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 4.457, "width_percent": 1.467}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.898879, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 5.924, "width_percent": 1.281}, {"sql": "select * from `apply_jobs` where `apply_jobs`.`id` = '1890' limit 1", "type": "query", "params": [], "bindings": ["1890"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 50}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 380}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}], "start": **********.906334, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Read.php:76", "source": {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=76", "ajax": false, "filename": "Read.php", "line": "76"}, "connection": "c_hri", "explain": null, "start_percent": 7.205, "width_percent": 1.058}, {"sql": "select * from `jobs` where `jobs`.`id` = 298 and `jobs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 165}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.90992, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:381", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=381", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "381"}, "connection": "c_hri", "explain": null, "start_percent": 8.264, "width_percent": 1.021}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 73}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.924093, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 9.285, "width_percent": 1.077}, {"sql": "select * from `career_levels` where `career_levels`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 92}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.926708, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:92", "source": {"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=92", "ajax": false, "filename": "show.blade.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 10.362, "width_percent": 0.799}, {"sql": "select `skills`.*, `cv_skills`.`cv_id` as `pivot_cv_id`, `cv_skills`.`skill_id` as `pivot_skill_id` from `skills` inner join `cv_skills` on `skills`.`id` = `cv_skills`.`skill_id` where `cv_skills`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.928588, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:105", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=105", "ajax": false, "filename": "show.blade.php", "line": "105"}, "connection": "c_hri", "explain": null, "start_percent": 11.161, "width_percent": 1.133}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 115}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.930827, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:115", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=115", "ajax": false, "filename": "show.blade.php", "line": "115"}, "connection": "c_hri", "explain": null, "start_percent": 12.293, "width_percent": 1.021}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.932503, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 13.315, "width_percent": 1.021}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 10:11:16', '2025-07-29 10:11:16', '2025-07-29 10:11:16')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 10:11:16", "2025-07-29 10:11:16", "2025-07-29 10:11:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.93469, "duration": 0.00887, "duration_str": "8.87ms", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 14.336, "width_percent": 16.472}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.946436, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 30.808, "width_percent": 1.021}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 10:11:16', '2025-07-29 10:11:16', '2025-07-29 10:11:16')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 10:11:16", "2025-07-29 10:11:16", "2025-07-29 10:11:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.948113, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 31.829, "width_percent": 1.226}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 137}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.949668, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 33.055, "width_percent": 1.021}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 10:11:16', '2025-07-29 10:11:16', '2025-07-29 10:11:16')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 10:11:16", "2025-07-29 10:11:16", "2025-07-29 10:11:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 137}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.951359, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 34.076, "width_percent": 1.3}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 146}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.953971, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 35.376, "width_percent": 1.077}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 10:11:16', '2025-07-29 10:11:16', '2025-07-29 10:11:16')", "type": "query", "params": [], "bindings": [115121, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 10:11:16", "2025-07-29 10:11:16", "2025-07-29 10:11:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 146}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.955625, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 36.453, "width_percent": 1.151}, {"sql": "select * from `meta` where `meta`.`owner_type` = 'App\\\\Models\\\\ApplyJob' and `meta`.`owner_id` = 1890 and `meta`.`owner_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\ApplyJob", 1890], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 118}, {"index": 22, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/GetMeta.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\GetMeta.php", "line": 24}, {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 150}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753758677.0479481, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 37.604, "width_percent": 1.523}, {"sql": "select * from `status` where `status`.`id` = 81 limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753758677.052929, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 39.127, "width_percent": 0.761}, {"sql": "select * from `status` where `status`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753758677.05444, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:226", "source": {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=226", "ajax": false, "filename": "show.blade.php", "line": "226"}, "connection": "c_hri", "explain": null, "start_percent": 39.889, "width_percent": 0.687}, {"sql": "select * from `notifications` where `user_id` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": 1753758677.1158469, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ClientViewComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FViewComposers%2FClientViewComposer.php&line=19", "ajax": false, "filename": "ClientViewComposer.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 40.576, "width_percent": 1.114}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753758677.121167, "duration": 0.02088, "duration_str": "20.88ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 41.69, "width_percent": 38.774}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753758677.1595361, "duration": 0.005889999999999999, "duration_str": "5.89ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 80.464, "width_percent": 10.938}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753758677.169427, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 91.402, "width_percent": 8.598}]}, "models": {"data": {"App\\Models\\Notification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FNotification.php&line=1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\Cv": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}, "App\\Models\\Status": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FStatus.php&line=1", "ajax": false, "filename": "Status.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ApplyJob": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\Job": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\CareerLevel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLevel.php&line=1", "ajax": false, "filename": "CareerLevel.php", "line": "?"}}, "App\\Models\\Skill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FSkill.php&line=1", "ajax": false, "filename": "Skill.php", "line": "?"}}, "App\\Models\\CareerLanguage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLanguage.php&line=1", "ajax": false, "filename": "CareerLanguage.php", "line": "?"}}}, "count": 24, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 45, "messages": [{"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1085016744 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085016744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.90377, "xdebug_link": null}, {"message": "[\n  ability => apply-job.edit,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1371534663 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371534663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.904887, "xdebug_link": null}, {"message": "[\n  ability => apply-job.show,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-226608875 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.show </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.show</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226608875\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.90551, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-31593873 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31593873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.085104, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1487738458 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487738458\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.087076, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.08806, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.089043, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-909507078 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909507078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.091404, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1145953739 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145953739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.092228, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-572869509 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572869509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.093022, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119630779 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119630779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.094589, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1995910667 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995910667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.095485, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155028644 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155028644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.096306, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1565071491 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565071491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.097147, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2086901702 data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086901702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.098012, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.100024, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009020037 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009020037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.101185, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-133456920 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133456920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.101932, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1023091684 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023091684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.102971, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-203040622 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203040622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.10399, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1723612012 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723612012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.106361, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1320173752 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320173752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.107582, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-391139956 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391139956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753758677.109781, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2096953726 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096953726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753758677.111585, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-697833477 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697833477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.179417, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.180837, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.181798, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119994592 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119994592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.182809, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-441381372 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441381372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.184699, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-280032547 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280032547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.185566, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1145056175 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145056175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.186408, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1556693470 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556693470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.187873, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1656609989 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656609989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.188758, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-376188532 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376188532\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.189637, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-475648773 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475648773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.190521, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1359982400 data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359982400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.191434, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2071325686 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071325686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.193576, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.194749, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1880384349 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880384349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.195502, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-899685334 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899685334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.19654, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882724754 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882724754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.197818, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-374012578 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374012578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.200109, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-999909857 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999909857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753758677.201316, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-294570078 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294570078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753758677.203167, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1359169744 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359169744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753758677.204794, "xdebug_link": null}]}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job/1890/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/apply-job/1890/show", "status_code": "<pre class=sf-dump id=sf-dump-194354205 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-194354205\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1156219201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1156219201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1117844098 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1117844098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1557043002 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZSMWwrL0RjSE8xTG9xSzBSM203NlE9PSIsInZhbHVlIjoiOXc0WVJ3dWRUUCt0Z1ErY1ZvK1lFWW9DN3hFRmlkOXUra2JlOEdFdE9wMllEMmdENHp1cjZ3cW80Ukh0RFFndVRSOTE4anl6NytrMWJ5UWt4bjc0UG9OTFFaWjgxbWFKbEVhdTdLMlRzTWhjZ0c0QmZKUXRZK2o5WkxDYndzQ3giLCJtYWMiOiI4MDNhMWI4ZWM2MzFmYWE4OWQyMjBjYTU4ZGM1Yzc2YjVjYTU5YmZhMTY3YzQ1YzU2ZDY4OWQxNTYxZjdlODZmIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IkdwUlVmdzVJeHRnTHFoM3lZeVozR3c9PSIsInZhbHVlIjoiK0JWQk1GR1pxYlFwbVVTV1A0UXZCRHdoWUxvcGx3dFhhaHdDZjZicDgrYzZtaGVSTDdZaWZ6bzZNSGxFdWxlZlMrcWdTbmt6YmFOMVdlRERWSWFTd0pMVzE3K2x3WVpPK0xUNTRSNElic0xyc0MwZTgrMmw0RDJ3R05rOUMwMDkiLCJtYWMiOiJiYjVlNTM2ZTNhZmZiMTIyOThjMzNkY2FiOTdjM2U3MTc1MmYyYjgzNDMyNDYxM2QxMTFkYTEyZTU3OGJhZDg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557043002\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1276458670 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276458670\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1140565754 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:11:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpUVjRQdHZhY2hrTUt0aHNYVHBJeGc9PSIsInZhbHVlIjoiejNTK2dLVzc0dTUrMjNHMjFkbi9oMExDRTdEUmpTeVJTbVpWNmVvb2J5V2p2Vjd2MFZXSzA5TUt2bXhzUDBVeVhEOTllRlFTMVFtL1EwY0dOclBZU045a0VlL3ducDAyNkxVYks3ZTNMbW8rSmVjQmR3Q1pCVEZJeWZ0c3d6d2ciLCJtYWMiOiJiNDQ2NTc5MmNiYTUwZDkxMmFhNzdkMGJjNmZjZjczNDU0MWZlMTdmYjYxM2RkNGQxNDFjMjE4OGMxOGRhZWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:11:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6ImZJTlUyV0JQcEpXTC9zNEc5VVBtK1E9PSIsInZhbHVlIjoiY2hYb21hc1VMNnR5WDlScVFLY01KZWQ0VExJVmlQU2lEU2NBL2p6NnBEVFVuY1kwcWVMMkNrMnlmQ3dhWVNtZ085SG9SZkxwQlBWbEZZd1lPUnU0QmpMV3FITXFSVTNuSmxzL2ltNERHWm5yWlBtODdud2xwK2VTbXhvVVZPOUMiLCJtYWMiOiJmNjY1NDA4NTQ1YjAwMTkzNzIyMjQxYjA5ZjY2MmQxOTZmYzQxNzkxZmE3ZTM4MTcyYTM1YWE5ZTU2Y2NmZGU2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:11:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpUVjRQdHZhY2hrTUt0aHNYVHBJeGc9PSIsInZhbHVlIjoiejNTK2dLVzc0dTUrMjNHMjFkbi9oMExDRTdEUmpTeVJTbVpWNmVvb2J5V2p2Vjd2MFZXSzA5TUt2bXhzUDBVeVhEOTllRlFTMVFtL1EwY0dOclBZU045a0VlL3ducDAyNkxVYks3ZTNMbW8rSmVjQmR3Q1pCVEZJeWZ0c3d6d2ciLCJtYWMiOiJiNDQ2NTc5MmNiYTUwZDkxMmFhNzdkMGJjNmZjZjczNDU0MWZlMTdmYjYxM2RkNGQxNDFjMjE4OGMxOGRhZWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:11:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6ImZJTlUyV0JQcEpXTC9zNEc5VVBtK1E9PSIsInZhbHVlIjoiY2hYb21hc1VMNnR5WDlScVFLY01KZWQ0VExJVmlQU2lEU2NBL2p6NnBEVFVuY1kwcWVMMkNrMnlmQ3dhWVNtZ085SG9SZkxwQlBWbEZZd1lPUnU0QmpMV3FITXFSVTNuSmxzL2ltNERHWm5yWlBtODdud2xwK2VTbXhvVVZPOUMiLCJtYWMiOiJmNjY1NDA4NTQ1YjAwMTkzNzIyMjQxYjA5ZjY2MmQxOTZmYzQxNzkxZmE3ZTM4MTcyYTM1YWE5ZTU2Y2NmZGU2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:11:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140565754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1877446361 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1877446361\", {\"maxDepth\":0})</script>\n"}}