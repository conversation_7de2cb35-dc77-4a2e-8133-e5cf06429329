{"__meta": {"id": "Xe128cbb87e06d6b5a12f61afe1072224", "datetime": "2025-07-29 10:52:23", "utime": **********.110998, "method": "GET", "uri": "/admin/secure-file/download?id=115121&model=Cv&field=cv_public&hash=48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.676015, "end": **********.111014, "duration": 0.4349989891052246, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.676015, "relative_start": 0, "end": **********.929412, "relative_end": **********.929412, "duration": 0.25339698791503906, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.929422, "relative_start": 0.25340700149536133, "end": **********.111017, "relative_end": 3.0994415283203125e-06, "duration": 0.1815950870513916, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31905104, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/secure-file/download", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\FileDownloadController@download", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "where": [], "as": "secure-file.download", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=25\" onclick=\"\">app/Http/Controllers/FileDownloadController.php:25-151</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.037029999999999993, "accumulated_duration_str": "37.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.973224, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.985317, "duration": 0.027469999999999998, "duration_str": "27.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 74.183}, {"sql": "select count(*) as aggregate from `file_access_logs` where `ip_address` = '127.0.0.1' and `accessed_at` >= '2025-07-29 10:42:23' and `is_successful` = 1", "type": "query", "params": [], "bindings": ["127.0.0.1", "2025-07-29 10:42:23", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Services/FileSecurityService.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Services\\FileSecurityService.php", "line": 21}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0589778, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:71", "source": {"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=71", "ajax": false, "filename": "FileAccessLog.php", "line": "71"}, "connection": "c_hri", "explain": null, "start_percent": 74.183, "width_percent": 1.647}, {"sql": "select * from `cvs` where `cvs`.`id` = '115121' and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["115121"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 102}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0635579, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "FileDownloadController.php:102", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 102}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=102", "ajax": false, "filename": "FileDownloadController.php", "line": "102"}, "connection": "c_hri", "explain": null, "start_percent": 75.83, "width_percent": 1.62}, {"sql": "insert into `file_access_logs` (`user_id`, `file_path`, `model_type`, `record_id`, `field_name`, `ip_address`, `user_agent`, `hash_token`, `is_successful`, `error_message`, `accessed_at`, `updated_at`, `created_at`) values (null, 'https://kho-cv.s3.ap-southeast-1.amazonaws.com/crm-hri-2023/cvs/202506/5a0a849603d5bad03092536000c49008.pdf', 'Cv', '115121', 'cv_public', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210', 1, null, '2025-07-29 10:52:23', '2025-07-29 10:52:23', '2025-07-29 10:52:23')", "type": "query", "params": [], "bindings": [null, "https://kho-cv.s3.ap-southeast-1.amazonaws.com/crm-hri-2023/cvs/202506/5a0a849603d5bad03092536000c49008.pdf", "Cv", "115121", "cv_public", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210", 1, null, "2025-07-29 10:52:23", "2025-07-29 10:52:23", "2025-07-29 10:52:23"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.066828, "duration": 0.00835, "duration_str": "8.35ms", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:81", "source": {"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=81", "ajax": false, "filename": "FileAccessLog.php", "line": "81"}, "connection": "c_hri", "explain": null, "start_percent": 77.451, "width_percent": 22.549}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Cv": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/secure-file/download?field=cv_public&hash=48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210&id=115121&model=Cv\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/secure-file/download", "status_code": "<pre class=sf-dump id=sf-dump-1272461913 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1272461913\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-22824627 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115121</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Cv</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"9 characters\">cv_public</span>\"\n  \"<span class=sf-dump-key>hash</span>\" => \"<span class=sf-dump-str title=\"64 characters\">48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22824627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-999833994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-999833994\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-170103377 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlMxbkxUNmw0eGpDWXdOaXRiVG5nY0E9PSIsInZhbHVlIjoiU01kaHlLVDN1RHJCRDVNSjN6MkJoRzZOcmpQZnA0Q1FXUWtaMS91b09HcFdyWmpaWHh1aHZKSUJVY25CT2FWSlhZbU5VRVFiQVJFTVF6R2lvRCtIOGNCODlaTkxla2IyeGJZNStaOWcxNEhRb3BlMzdOa1k4clZrbCtOSXVEVkoiLCJtYWMiOiJkODc0MGYzMTA3N2ZkOTllYzI2YTFkY2RlY2M2NGZkNmNhZmIzNWM4MWUyMmExZGMxNDA1MjM4MjEyYzAwNzI0IiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IkY4QnYxNWhuZ2lDMXoyQ29oU3luY3c9PSIsInZhbHVlIjoiR1lIUlg2MXJPNjhwZHlkMlRIZHovd1VqUWxMeC9QaWZJb1I0M2FlVkE4WW1KSm1EbmlFOWhxY2pPYnlSSVIzMStXMlc4blhXcHNGTVhXalh4bCtEWTBTVzh3UkZNSDVyVXlQNy8xeUpyU05hQ0k2amZNN1Vjak1iM0V0UzM3VWwiLCJtYWMiOiJjNjJmZDgxNzU0NDQ1NGE3YjE5ODFhOTYwNjI5OTgyY2MzNzg3NjFkZDNhOTI2Y2I4YmNlNDk2OTBmODgxYzExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170103377\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1129560473 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129560473\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:52:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"107 characters\">https://kho-cv.s3.ap-southeast-1.amazonaws.com/crm-hri-2023/cvs/202506/5a0a849603d5bad03092536000c49008.pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1PYnc1TVFjWUIrWjVGTnVPZ1k2d3c9PSIsInZhbHVlIjoiOWdiTjliclF6S01DZGhVQXo0bU9LN01NZ2ZtZVR1ZUg2b0I4WFgxOGlLMFlvSForZVdFY0IvaWk4UUsrQWprT0lyU1J5NnJ3UTNTK2YxdXMyOUUraWhWQVRpV0lkUzFOUmV3SHE2TEd3cXhYV1R6amtwczdkY2pPbjRRbVBHRS8iLCJtYWMiOiJlMGQwYTYzZDYzMGMxNzJmMGQzNTg1M2JlZWZiYzVlNTVjNTQ3MjhiMTZiY2MwYTJjZmViZDYyNzkyNjkyZDc5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:52:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IkxiUzdoQzJqc1dsakQ5Y3JWWXpuWVE9PSIsInZhbHVlIjoiREg4TStxblUxRTBjZkxNWlp5MnBMdE92ZUdkU09YV25WOGhqb3F1cFpzQXQyb1JURWhITFNDdk94cVNCdWNML1hKYU9IQU4yYzFYRmM2K05jNld5eE1waW9HSXFDK3pvQ3B4OC9DWVNScHNhM3o5OU9VbGtrMWt5ajUxbTUyeWMiLCJtYWMiOiJlZmY0YTZkM2Q1YTBlMzQxMjA5Y2ViYzY3NmViZGYwNmU3N2M3N2Q4MTFkYjMzMmI2MmIzNjZiNjRmYWQzMTQxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:52:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1PYnc1TVFjWUIrWjVGTnVPZ1k2d3c9PSIsInZhbHVlIjoiOWdiTjliclF6S01DZGhVQXo0bU9LN01NZ2ZtZVR1ZUg2b0I4WFgxOGlLMFlvSForZVdFY0IvaWk4UUsrQWprT0lyU1J5NnJ3UTNTK2YxdXMyOUUraWhWQVRpV0lkUzFOUmV3SHE2TEd3cXhYV1R6amtwczdkY2pPbjRRbVBHRS8iLCJtYWMiOiJlMGQwYTYzZDYzMGMxNzJmMGQzNTg1M2JlZWZiYzVlNTVjNTQ3MjhiMTZiY2MwYTJjZmViZDYyNzkyNjkyZDc5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:52:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IkxiUzdoQzJqc1dsakQ5Y3JWWXpuWVE9PSIsInZhbHVlIjoiREg4TStxblUxRTBjZkxNWlp5MnBMdE92ZUdkU09YV25WOGhqb3F1cFpzQXQyb1JURWhITFNDdk94cVNCdWNML1hKYU9IQU4yYzFYRmM2K05jNld5eE1waW9HSXFDK3pvQ3B4OC9DWVNScHNhM3o5OU9VbGtrMWt5ajUxbTUyeWMiLCJtYWMiOiJlZmY0YTZkM2Q1YTBlMzQxMjA5Y2ViYzY3NmViZGYwNmU3N2M3N2Q4MTFkYjMzMmI2MmIzNjZiNjRmYWQzMTQxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:52:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"149 characters\">http://chri.local/admin/secure-file/download?field=cv_public&amp;hash=48082f6521123a032ff5c344284cbf77b77f889a3721a4389ac8b0c9c67d6210&amp;id=115121&amp;model=Cv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}