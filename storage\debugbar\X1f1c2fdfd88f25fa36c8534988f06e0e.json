{"__meta": {"id": "X1f1c2fdfd88f25fa36c8534988f06e0e", "datetime": "2025-07-29 09:57:41", "utime": 1753757861.384735, "method": "GET", "uri": "/admin/apply-job", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.073066, "end": 1753757861.384753, "duration": 2.3116869926452637, "duration_str": "2.31s", "measures": [{"label": "Booting", "start": **********.073066, "relative_start": 0, "end": **********.339762, "relative_end": **********.339762, "duration": 0.2666959762573242, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339773, "relative_start": 0.2667069435119629, "end": 1753757861.384755, "relative_end": 1.9073486328125e-06, "duration": 2.0449819564819336, "duration_str": "2.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 39665256, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 141, "templates": [{"name": "1x crud::list", "param_count": null, "params": [], "start": 1753757860.519491, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/list.blade.phpcrud::list", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Flist.blade.php&line=1", "ajax": false, "filename": "list.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::list"}, {"name": "1x crud::inc.button_stack", "param_count": null, "params": [], "start": 1753757860.524371, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.button_stack"}, {"name": "1x crud::buttons.create", "param_count": null, "params": [], "start": 1753757860.525139, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/buttons/create.blade.phpcrud::buttons.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fbuttons%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::buttons.create"}, {"name": "1x crud::inc.filters_navbar", "param_count": null, "params": [], "start": 1753757860.525773, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/filters_navbar.blade.phpcrud::inc.filters_navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Ffilters_navbar.blade.php&line=1", "ajax": false, "filename": "filters_navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.filters_navbar"}, {"name": "5x backpack.pro::filters.select2_multiple", "param_count": null, "params": [], "start": 1753757860.693244, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\pro/resources/views/filters/select2_multiple.blade.phpbackpack.pro::filters.select2_multiple", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fpro%2Fresources%2Fviews%2Ffilters%2Fselect2_multiple.blade.php&line=1", "ajax": false, "filename": "select2_multiple.blade.php", "line": "?"}, "render_count": 5, "name_original": "backpack.pro::filters.select2_multiple"}, {"name": "15x __components::723fe3f0e44fe2b3529303522562360e", "param_count": null, "params": [], "start": 1753757860.878536, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/723fe3f0e44fe2b3529303522562360e.blade.php__components::723fe3f0e44fe2b3529303522562360e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F723fe3f0e44fe2b3529303522562360e.blade.php&line=1", "ajax": false, "filename": "723fe3f0e44fe2b3529303522562360e.blade.php", "line": "?"}, "render_count": 15, "name_original": "__components::723fe3f0e44fe2b3529303522562360e"}, {"name": "23x __components::8a07812e6d8f9d2826754abad88e5380", "param_count": null, "params": [], "start": 1753757860.881722, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/8a07812e6d8f9d2826754abad88e5380.blade.php__components::8a07812e6d8f9d2826754abad88e5380", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F8a07812e6d8f9d2826754abad88e5380.blade.php&line=1", "ajax": false, "filename": "8a07812e6d8f9d2826754abad88e5380.blade.php", "line": "?"}, "render_count": 23, "name_original": "__components::8a07812e6d8f9d2826754abad88e5380"}, {"name": "1x backpack.pro::filters.select2", "param_count": null, "params": [], "start": 1753757860.89668, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\pro/resources/views/filters/select2.blade.phpbackpack.pro::filters.select2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fpro%2Fresources%2Fviews%2Ffilters%2Fselect2.blade.php&line=1", "ajax": false, "filename": "select2.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.pro::filters.select2"}, {"name": "1x backpack.pro::filters.date_range", "param_count": null, "params": [], "start": 1753757861.047261, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\pro/resources/views/filters/date_range.blade.phpbackpack.pro::filters.date_range", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fpro%2Fresources%2Fviews%2Ffilters%2Fdate_range.blade.php&line=1", "ajax": false, "filename": "date_range.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.pro::filters.date_range"}, {"name": "1x crud::inc.datatables_logic", "param_count": null, "params": [], "start": 1753757861.191724, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/datatables_logic.blade.phpcrud::inc.datatables_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fdatatables_logic.blade.php&line=1", "ajax": false, "filename": "datatables_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.datatables_logic"}, {"name": "1x crud::inc.export_buttons", "param_count": null, "params": [], "start": 1753757861.227139, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/export_buttons.blade.phpcrud::inc.export_buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fexport_buttons.blade.php&line=1", "ajax": false, "filename": "export_buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.export_buttons"}, {"name": "1x crud::inc.details_row_logic", "param_count": null, "params": [], "start": 1753757861.2341, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/details_row_logic.blade.phpcrud::inc.details_row_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fdetails_row_logic.blade.php&line=1", "ajax": false, "filename": "details_row_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.details_row_logic"}, {"name": "1x backpack.theme-tabler::blank", "param_count": null, "params": [], "start": 1753757861.235135, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/blank.blade.phpbackpack.theme-tabler::blank", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::blank"}, {"name": "4x backpack.theme-tabler::inc.widgets", "param_count": null, "params": [], "start": 1753757861.236251, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/widgets.blade.phpbackpack.theme-tabler::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.theme-tabler::inc.widgets"}, {"name": "1x backpack.theme-tabler::layouts.horizontal_dark", "param_count": null, "params": [], "start": 1753757861.238598, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/horizontal_dark.blade.phpbackpack.theme-tabler::layouts.horizontal_dark", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fhorizontal_dark.blade.php&line=1", "ajax": false, "filename": "horizontal_dark.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.horizontal_dark"}, {"name": "1x backpack.theme-tabler::inc.head", "param_count": null, "params": [], "start": 1753757861.239666, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/head.blade.phpbackpack.theme-tabler::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.head"}, {"name": "1x backpack.theme-tabler::inc.theme_styles", "param_count": null, "params": [], "start": 1753757861.24115, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_styles.blade.phpbackpack.theme-tabler::inc.theme_styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_styles.blade.php&line=1", "ajax": false, "filename": "theme_styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_styles"}, {"name": "1x backpack.ui::inc.styles", "param_count": null, "params": [], "start": 1753757861.245313, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/styles.blade.phpbackpack.ui::inc.styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.styles"}, {"name": "1x backpack.theme-tabler::layouts.partials.light_dark_mode_logic", "param_count": null, "params": [], "start": 1753757861.254245, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/light_dark_mode_logic.blade.phpbackpack.theme-tabler::layouts.partials.light_dark_mode_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Flight_dark_mode_logic.blade.php&line=1", "ajax": false, "filename": "light_dark_mode_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.light_dark_mode_logic"}, {"name": "1x backpack.theme-tabler::layouts._horizontal_dark.menu_container", "param_count": null, "params": [], "start": 1753757861.2555, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal_dark/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal_dark.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal_dark%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal_dark.menu_container"}, {"name": "1x backpack.theme-tabler::layouts._horizontal.menu_container", "param_count": null, "params": [], "start": 1753757861.256153, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal.menu_container"}, {"name": "2x backpack.theme-tabler::inc.sidebar_content", "param_count": null, "params": [], "start": 1753757861.257708, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/sidebar_content.blade.phpbackpack.theme-tabler::inc.sidebar_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fsidebar_content.blade.php&line=1", "ajax": false, "filename": "sidebar_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.sidebar_content"}, {"name": "2x backpack.ui::inc.menu_items", "param_count": null, "params": [], "start": 1753757861.259008, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.phpbackpack.ui::inc.menu_items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fui%2Finc%2Fmenu_items.blade.php&line=1", "ajax": false, "filename": "menu_items.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.ui::inc.menu_items"}, {"name": "44x backpack.theme-tabler::components.menu-dropdown-item", "param_count": null, "params": [], "start": 1753757861.263973, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown-item.blade.phpbackpack.theme-tabler::components.menu-dropdown-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown-item.blade.php&line=1", "ajax": false, "filename": "menu-dropdown-item.blade.php", "line": "?"}, "render_count": 44, "name_original": "backpack.theme-tabler::components.menu-dropdown-item"}, {"name": "12x backpack.theme-tabler::components.menu-dropdown", "param_count": null, "params": [], "start": 1753757861.267201, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown.blade.phpbackpack.theme-tabler::components.menu-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown.blade.php&line=1", "ajax": false, "filename": "menu-dropdown.blade.php", "line": "?"}, "render_count": 12, "name_original": "backpack.theme-tabler::components.menu-dropdown"}, {"name": "1x backpack.theme-tabler::inc.menu", "param_count": null, "params": [], "start": 1753757861.289126, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu.blade.phpbackpack.theme-tabler::inc.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu"}, {"name": "2x backpack.theme-tabler::inc.topbar_left_content", "param_count": null, "params": [], "start": 1753757861.289825, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_left_content.blade.phpbackpack.theme-tabler::inc.topbar_left_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_left_content.blade.php&line=1", "ajax": false, "filename": "topbar_left_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_left_content"}, {"name": "2x backpack.theme-tabler::layouts.partials.switch_theme", "param_count": null, "params": [], "start": 1753757861.290444, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/switch_theme.blade.phpbackpack.theme-tabler::layouts.partials.switch_theme", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fswitch_theme.blade.php&line=1", "ajax": false, "filename": "switch_theme.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::layouts.partials.switch_theme"}, {"name": "1x backpack.theme-tabler::inc.menu_notification_dropdown", "param_count": null, "params": [], "start": 1753757861.294403, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu_notification_dropdown.blade.phpbackpack.theme-tabler::inc.menu_notification_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu_notification_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_notification_dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu_notification_dropdown"}, {"name": "2x backpack.theme-tabler::inc.topbar_right_content", "param_count": null, "params": [], "start": 1753757861.330162, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_right_content.blade.phpbackpack.theme-tabler::inc.topbar_right_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_right_content.blade.php&line=1", "ajax": false, "filename": "topbar_right_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_right_content"}, {"name": "2x backpack.theme-tabler::inc.menu_user_dropdown", "param_count": null, "params": [], "start": 1753757861.330786, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/menu_user_dropdown.blade.phpbackpack.theme-tabler::inc.menu_user_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fmenu_user_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_user_dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.menu_user_dropdown"}, {"name": "1x backpack.theme-tabler::layouts.partials.mobile_toggle_btn", "param_count": null, "params": [], "start": 1753757861.337398, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/mobile_toggle_btn.blade.phpbackpack.theme-tabler::layouts.partials.mobile_toggle_btn", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fmobile_toggle_btn.blade.php&line=1", "ajax": false, "filename": "mobile_toggle_btn.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.mobile_toggle_btn"}, {"name": "1x backpack.theme-tabler::inc.breadcrumbs", "param_count": null, "params": [], "start": 1753757861.372639, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/breadcrumbs.blade.phpbackpack.theme-tabler::inc.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.breadcrumbs"}, {"name": "1x backpack.theme-tabler::inc.footer", "param_count": null, "params": [], "start": 1753757861.373469, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/footer.blade.phpbackpack.theme-tabler::inc.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.footer"}, {"name": "1x backpack.ui::inc.scripts", "param_count": null, "params": [], "start": 1753757861.37502, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/scripts.blade.phpbackpack.ui::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.scripts"}, {"name": "1x backpack.theme-tabler::inc.alerts", "param_count": null, "params": [], "start": 1753757861.37773, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/alerts.blade.phpbackpack.theme-tabler::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": 1753757861.378552, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}, {"name": "1x backpack.theme-tabler::inc.theme_scripts", "param_count": null, "params": [], "start": 1753757861.380209, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_scripts.blade.phpbackpack.theme-tabler::inc.theme_scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_scripts.blade.php&line=1", "ajax": false, "filename": "theme_scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_scripts"}]}, "route": {"uri": "GET admin/apply-job", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin, Closure", "as": "apply-job.index", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\ApplyJobCrudController@index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=56\" onclick=\"\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:56-65</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10505999999999999, "accumulated_duration_str": "105ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.38421, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.396404, "duration": 0.022, "duration_str": "22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 20.94}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": 1753757860.255403, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 20.94, "width_percent": 0.847}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": 1753757860.260077, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 21.788, "width_percent": 0.685}, {"sql": "select `company_abbreviation`, `id` from `companies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 403}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 396}], "start": 1753757860.329816, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:403", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=403", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "403"}, "connection": "c_hri", "explain": null, "start_percent": 22.473, "width_percent": 0.647}, {"sql": "select distinct `created_by` from `jobs` where `jobs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 418}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 411}], "start": 1753757860.332759, "duration": 0.00531, "duration_str": "5.31ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:418", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=418", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "418"}, "connection": "c_hri", "explain": null, "start_percent": 23.12, "width_percent": 5.054}, {"sql": "select `email`, `id` from `users` where `id` in (19, 70, 20, 30, 25, 45, 22, 21, 5, 46, 69, 67, 1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [19, 70, 20, 30, 25, 45, 22, 21, 5, 46, 69, 67, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 419}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 411}], "start": 1753757860.339232, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:419", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 419}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=419", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "419"}, "connection": "c_hri", "explain": null, "start_percent": 28.174, "width_percent": 0.524}, {"sql": "select distinct `created_by` from `apply_jobs`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 442}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 435}], "start": 1753757860.341445, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:442", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=442", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "442"}, "connection": "c_hri", "explain": null, "start_percent": 28.698, "width_percent": 2.256}, {"sql": "select `email`, `id` from `users` where `id` in (1, 20, 28, 33, 22, 18, 24, 15, 37, 17, 42, 43, 29, 34, 38, 36, 45, 47, 25, 48, 49, 52, 54, 59, 57, 61, 56, 58, 60, 62, 64, 66, 65) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 20, 28, 33, 22, 18, 24, 15, 37, 17, 42, 43, 29, 34, 38, 36, 45, 47, 25, 48, 49, 52, 54, 59, 57, 61, 56, 58, 60, 62, 64, 66, 65], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 443}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 435}], "start": 1753757860.344964, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:443", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 443}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=443", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "443"}, "connection": "c_hri", "explain": null, "start_percent": 30.954, "width_percent": 0.6}, {"sql": "select `title`, `id` from `jobs` where `jobs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 466}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 459}], "start": 1753757860.34682, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:466", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 466}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=466", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "466"}, "connection": "c_hri", "explain": null, "start_percent": 31.553, "width_percent": 0.999}, {"sql": "select `name`, `id` from `status` where `group` = 'apply-job' and `parent_id` is null", "type": "query", "params": [], "bindings": ["apply-job"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 482}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 475}], "start": 1753757860.349448, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:482", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=482", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "482"}, "connection": "c_hri", "explain": null, "start_percent": 32.553, "width_percent": 0.476}, {"sql": "select `name`, `id` from `status` where `group` = 'job-type' and `parent_id` is null", "type": "query", "params": [], "bindings": ["job-type"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 497}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 490}], "start": 1753757860.351111, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:497", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 497}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=497", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "497"}, "connection": "c_hri", "explain": null, "start_percent": 33.029, "width_percent": 0.466}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'apply_jobs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 332}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 384}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 343}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 277}], "start": 1753757860.397227, "duration": 0.01966, "duration_str": "19.66ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:332", "source": {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 332}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=332", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "332"}, "connection": "c_hri", "explain": null, "start_percent": 33.495, "width_percent": 18.713}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'jobs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.449118, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 52.208, "width_percent": 3.712}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'jobs' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.454329, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 55.92, "width_percent": 0.961}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'status' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.489366, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 56.882, "width_percent": 2.77}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'status' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.493283, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 59.652, "width_percent": 0.895}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'candidates' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.4960341, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 60.546, "width_percent": 2.789}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'candidates' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.499985, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 63.335, "width_percent": 0.914}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'cvs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.503107, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 64.249, "width_percent": 2.932}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'cvs' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.507264, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 67.181, "width_percent": 1.047}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.5096688, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 68.228, "width_percent": 2.741}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'users' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": 1753757860.513547, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 70.969, "width_percent": 0.866}, {"sql": "select * from `notifications` where `user_id` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": 1753757861.2920668, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ClientViewComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FViewComposers%2FClientViewComposer.php&line=19", "ajax": false, "filename": "ClientViewComposer.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 71.835, "width_percent": 0.733}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757861.295089, "duration": 0.02038, "duration_str": "20.38ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 72.568, "width_percent": 19.398}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757861.331202, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 91.966, "width_percent": 4.026}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757861.338992, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 95.993, "width_percent": 4.007}]}, "models": {"data": {"App\\Models\\Notification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FNotification.php&line=1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 45, "messages": [{"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107717320 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107717320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757860.26732, "xdebug_link": null}, {"message": "[\n  ability => apply-job.edit,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190331501 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190331501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757860.268455, "xdebug_link": null}, {"message": "[\n  ability => apply-job.show,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-473015680 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.show </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.show</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473015680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757860.269065, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-933769984 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933769984\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.263059, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1675598189 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675598189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.26493, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.265816, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949784060 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949784060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.266731, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1312322533 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312322533\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.269071, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1709744525 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709744525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.269856, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-524421302 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524421302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.270636, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-145896794 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145896794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.271961, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-473904463 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473904463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.272763, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1416991579 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416991579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.273556, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-756773447 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756773447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.274366, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1194456266 data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194456266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.275196, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1701902312 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701902312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.277155, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2098041094 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098041094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.278237, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1704834425 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704834425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.278915, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-8992444 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8992444\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.27985, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-720566338 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720566338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.280764, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-540475600 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540475600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.282976, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1970203053 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970203053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.284769, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615371637 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615371637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757861.28678, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-507581726 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507581726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757861.288327, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907087672 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907087672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.348283, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.349321, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.350207, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1287485505 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287485505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.351121, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273498901 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273498901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.352916, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394205720 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394205720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.353705, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-954174461 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954174461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.354473, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1116336765 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116336765\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.355797, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1282947754 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282947754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.356604, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084795451 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084795451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.357396, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1660186348 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660186348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.358201, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.359037, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1796474337 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796474337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.360984, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.362464, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-369785200 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369785200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.363177, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-323039876 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323039876\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.364117, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1841001652 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841001652\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.365033, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-475335822 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475335822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.367231, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-645542536 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645542536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757861.368446, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-735411127 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735411127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757861.370243, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-424425576 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424425576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757861.371786, "xdebug_link": null}]}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/apply-job", "status_code": "<pre class=sf-dump id=sf-dump-1681307302 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1681307302\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2025602044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2025602044\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-733783443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-733783443\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im95b3lVSHdwWVFwZ2JtZkpheC9zV0E9PSIsInZhbHVlIjoiKzg0d3MrcVNxKzFhUG92QmhYUGh0UEJESzZEMjJrcjIrU2VxL1N6dm8wQ2VMTFVZakVTQWJXcnVMdFR3Ty9PazdmNFZkdHZDK3p6US85VHRrSnJBKzlSaVdGaTBpMFlMVDlEb1BaUWo2bDFwRjljNkNDc3dQRVk4UmpMaCtiNm4iLCJtYWMiOiI2YmMwYzQwODFmOWIxOTc1MTllMTI4NzIwMDQzY2QyM2Q5NGFjMGE3ZjA5ZDhjMjJmZWM2MmQ0ZGYxNjg1OTU2IiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImhaZ1pnSTAxcDNLa1JwYjBLZm1RSGc9PSIsInZhbHVlIjoiYk1iam8vd3NFTXBJeDBkU0FhT1dGZ0xxcmVUbkUraUJrSHp0eEdpblp0ZnlLR2VocU56bVZVMERMdkVweWZReFpGQzZ6YUNyMVcvbXg4Q2JkSWFtbSt5RzRzbm9tYzlsWWRLT0pEYmM5K0NjbHdiM2FBL1dlbWx5aUdOVkgwcmoiLCJtYWMiOiI5NzNjMmE5ZGU0ZjhhMjkxODk4NzY4MThjZjhhOTA5ZGU0NTk4N2MxOGVhYjg5ZTU2ZWJhMDI2OGQ5NmQxZTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1316304132 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316304132\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImYxZloxdnV1Vjd1N1hBdklhVVhEU1E9PSIsInZhbHVlIjoiSjdlZjI1M3laTTVHY05OVmpLT0tnWE1kS2I2YzZUblFxNFF2MmZhZWNHSyticmVkTUYvVlhIS1U2dk9yak9DOGlMTktXZnVvdGZVTHBOaXUySUQ2VEI2NGNvUHBTd2p6M2lEUElzcUJFR2swT0IxVjJCM3k0ZldJVmFEdDlkT3YiLCJtYWMiOiI0YjM3ZDQ5YTE5MDk2ZTY5YzJjODVkM2E2MWFmNDU5YWUzMzQyZmNkOWJiOWVjNTYyNDg2YmY2YWQwYTQxNDAxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IlZxU1VwSGVpUStqWHZXKzZxYnREc0E9PSIsInZhbHVlIjoiTUlSNExhQTJUM0d3SUM3SGhhR210T01nR3VDZks0dkozT3ZSbDJEQTZES2w5NTE5WXdIWk92d1ZENUYrTWlnTGp4VFNLWkc1MXFhdkNpQWtDdHNIQkIzK0tmdEQ3ZkJ5S1BVUXFRSE9FWVYvRXNvUERzYnNUK2NiTHZLOTE4VmIiLCJtYWMiOiJjNWFiOGYxOWRhMDhkZmViNzRlNDIwOWI2MzRjNDQzMzgyMGM1MTE1MGYzMjgxYTM2NTIzMTMwOWUxMzZjNDJmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImYxZloxdnV1Vjd1N1hBdklhVVhEU1E9PSIsInZhbHVlIjoiSjdlZjI1M3laTTVHY05OVmpLT0tnWE1kS2I2YzZUblFxNFF2MmZhZWNHSyticmVkTUYvVlhIS1U2dk9yak9DOGlMTktXZnVvdGZVTHBOaXUySUQ2VEI2NGNvUHBTd2p6M2lEUElzcUJFR2swT0IxVjJCM3k0ZldJVmFEdDlkT3YiLCJtYWMiOiI0YjM3ZDQ5YTE5MDk2ZTY5YzJjODVkM2E2MWFmNDU5YWUzMzQyZmNkOWJiOWVjNTYyNDg2YmY2YWQwYTQxNDAxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IlZxU1VwSGVpUStqWHZXKzZxYnREc0E9PSIsInZhbHVlIjoiTUlSNExhQTJUM0d3SUM3SGhhR210T01nR3VDZks0dkozT3ZSbDJEQTZES2w5NTE5WXdIWk92d1ZENUYrTWlnTGp4VFNLWkc1MXFhdkNpQWtDdHNIQkIzK0tmdEQ3ZkJ5S1BVUXFRSE9FWVYvRXNvUERzYnNUK2NiTHZLOTE4VmIiLCJtYWMiOiJjNWFiOGYxOWRhMDhkZmViNzRlNDIwOWI2MzRjNDQzMzgyMGM1MTE1MGYzMjgxYTM2NTIzMTMwOWUxMzZjNDJmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}