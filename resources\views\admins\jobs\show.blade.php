@extends(backpack_view('blank'))

@php
$defaultBreadcrumbs = [
trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
$crud->entity_name_plural => url($crud->route),
trans('backpack::crud.preview') => false,
];

// if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
$breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;

// $status = \App\Models\Status::where('group', 'apply-job')->where('name', 'Đã ứng tuyển')->firstOrNone();

@endphp

@section('header')
<div class="container-fluid d-flex justify-content-between row">
    <section class="header-operation animated fadeIn d-flex mb-2 align-items-baseline d-print-none col-12"
        bp-section="page-header">
        <h1 class="text-capitalize mb-0" bp-section="page-heading">{!! $crud->getHeading() ?? $crud->entity_name_plural
            !!}</h1>
        <p class="ms-2 ml-2 mb-0" bp-section="page-subheading">{!! $crud->getSubheading() ??
            mb_ucfirst(trans('backpack::crud.preview')) . ' ' . $crud->entity_name !!}</p>
        @if ($crud->hasAccess('list'))
        <p class="ms-2 ml-2 mb-0" bp-section="page-subheading-back-button">
            <small><a href="{{ url($crud->route) }}" class="font-sm"><i class="la la-angle-double-left"></i>
                    {{ trans('backpack::crud.back_to_all') }}
                    <span>{{ $crud->entity_name_plural }}</span></a></small>
        </p>
        @endif

    </section>
    {{-- <section class="col-12 text-primary"> --}}
        {{-- <h1> {{ $entry->title }} </h1> --}}
        {{-- </section> --}}
</div>
@endsection

@section('content')
<div class="row" bp-section="crud-operation-show">
    <div class="{{ $crud->getShowContentClass() }}">

        {{-- Default box --}}
        <div class="row">
            @if ($crud->model->translationEnabled())
            <div class="row">
                <div class="col-md-12 mb-2">
                    {{-- Change translation button group --}}
                    <div class="btn-group float-right">
                        <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {{ trans('backpack::crud.language') }}
                            :
                            {{ $crud->model->getAvailableLocales()[request()->input('_locale') ?
                            request()->input('_locale') : App::getLocale()] }}
                            &nbsp; <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            @foreach ($crud->model->getAvailableLocales() as $key => $locale)
                            <a class="dropdown-item"
                                href="{{ url($crud->route . '/' . $entry->getKey() . '/show') }}?_locale={{ $key }}">{{
                                $locale }}</a>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endif
            <div class="col-12 ">
                <div class="contact__wrapper shadow-lg mt-n9">
                    <div class="row no-gutters">
                        {{-- <div class="col-lg-5 contact-info__wrapper gradient-brand-color p-5 order-lg-2"> --}}
                            <div class="bg-danger d-lg-inline-block py-1-9 px-1-9 px-sm-6 mb-1-9 rounded py-2"
                                style="margin-bottom: 15px">
                                <h3 class="h2 text-white mb-0">{{ $entry->title }}</h3>
                            </div>

                            <ul class="list-unstyled mb-1-9 row">
                                <div class="col-md-4">
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Loại JOB:</span>
                                        {{ optional($entry->typeJob)->name }}</li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Độ ưu tiên:</span>
                                        {{ optional($entry->priorityJob)->name }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">
                                            Kinh nghiệm:</span>
                                        @if (count($entry->levels))
                                        @foreach ($entry->levels as $level)
                                        {{ $level->name_vi }} <br>
                                        @endforeach
                                        @endif
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Số lượng:</span>
                                        {{ $entry->vacancy }}</li>
                                    <li class="mb-2 mb-xl-3 display-28">
                                        <span class="display-26 text-secondary me-2 font-weight-600">Job Rec:</span>
                                        {{ $entry->recerJob->pluck('name')->implode(', ') }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Ngày mở Job:</span>
                                        {{ \Carbon\Carbon::parse($entry->opening_date)->format('d/m/Y') }}</li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Hình thức kí
                                            HĐ:</span>
                                        {{ optional($entry->contractJob)->name }}</li>
                                </div>
                                <div class="col-md-4">
                                    @if(backpack_user()->can('job.show-company-info') || !$entry->hide_to_recer)
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Công
                                            ty:</span>{{ optional($entry->company)->name }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Mã số
                                            thuế:</span>{{ optional($entry->company)->mst }}
                                    </li>
                                    @endif
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Nhân viên phụ
                                            trách:</span>{{ optional($entry->user)->name }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Địa
                                            chỉ:</span>{{ $entry->address }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28">
                                        <span class="display-26 text-secondary me-2 font-weight-600">Job RecTeam:</span>
                                        {{ $entry->recTeam->pluck('name')->implode(', ') }}
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Hạn đóng Job:</span>
                                        {{ !empty($entry->closing_date) ?
                                        \Carbon\Carbon::parse($entry->closing_date)->format('d/m/Y') : '-' }}
                                    </li>
                                </div>
                                <div class="col-md-4">
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Tệp đính kèm:</span>
                                        @if (!is_null($entry->path))
                                        <a target="_blank" href="{{ secure_file_url($entry->id, 'Job', 'path') }}">
                                            {{ $entry->title }} </a>
                                        @else
                                        Không có tệp đính kèm
                                        @endif
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Trạng thái:</span>
                                        <button
                                            class="btn btn-{{ \Illuminate\Support\Str::slug(optional($entry->statusJob)->name) }}">
                                            <strong>{{ optional($entry->statusJob)->name }}</strong></button>
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28">
                                        <span class="display-26 text-secondary me-2 font-weight-600">Mức Lương:</span>
                                        @if (!$entry->min_salary && !$entry->max_salary)
                                        Thỏa thuận
                                        @else
                                        @if ($entry->min_salary && !$entry->max_salary)
                                        {{ \App\Helpers\Utils::money_format(intval($entry->min_salary)) }}
                                        {{ optional($entry->currencyJob)->name }}
                                        @elseif (!$entry->min_salary && $entry->max_salary)
                                        {{ \App\Helpers\Utils::money_format(intval($entry->max_salary)) }}
                                        {{ optional($entry->currencyJob)->name }}
                                        @else
                                        {{ \App\Helpers\Utils::money_format(intval($entry->min_salary)) }} -
                                        {{ \App\Helpers\Utils::money_format(intval($entry->max_salary)) }}
                                        {{ optional($entry->currencyJob)->name }}
                                        @endif
                                        @endif

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28">
                                        <span class="display-26 text-secondary me-2 font-weight-600">Kỹ năng:</span>
                                        {{ $entry->skills->pluck('name')->implode(', ') }}
                                    </li>
                                    @if (!empty($entry->note_opening_closing))
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26 text-secondary me-2 font-weight-600">Lý do đóng Job (nếu
                                            có):</span>
                                        {{ $entry->note_opening_closing }}</li>
                                    @endif

                                    @if ($crud->buttons()->where('stack', 'line')->count() && ($displayActionsColumn ??
                                    true))
                                    <tr>
                                        <td>
                                            <strong>{{ trans('backpack::crud.actions') }}</strong>
                                        </td>
                                        <td colspan="3">
                                            @include('crud::inc.button_stack', ['stack' => 'line'])
                                        </td>
                                    </tr>
                                    @endif
                                </div>
                            </ul>

                            <div>
                                <ul class="mb-2 mb-xl-3 display-28"><span
                                        class="display-26 text-secondary me-2 font-weight-600">Mô tả công việc:</span>
                                    {!! $entry->description !!}
                                </ul>
                            </div>
                            <div>
                                <ul class="mb-2 mb-xl-3 display-28"><span
                                        class="display-26 text-secondary me-2 font-weight-600">Yêu cầu:</span>
                                    {!! $entry->requirement !!}
                                </ul>
                            </div>
                            <div>
                                <ul class="mb-2 mb-xl-3 display-28"><span
                                        class="display-26 text-secondary me-2 font-weight-600">Quyền lợi:</span>
                                    {!! $entry->benefit !!}
                                </ul>
                            </div>


                            <div>
                                <ul class="mb-2 mb-xl-3 display-28"><span
                                        class="display-26 text-secondary me-2 font-weight-600">Ghi chú:</span>
                                    {!! $entry->note !!}
                                </ul>
                            </div>
                            {{--
                        </div> --}}
                        <!-- End Contact Form Wrapper -->
                    </div>
                </div>
                <div>
                    <button type="button"
                        class="btn bg-orange  btn btn-link  btn-lg float-end inline-create-button mt-3 "
                        data-bs-toggle="modal" data-bs-target="#inline-create-dialog"><i class="la la-file"></i>Ứng
                        tuyển
                    </button>
                    @php
                    $recland_job_id = $entry->meta('recland_job_id');
                    @endphp
                    @if ($recland_job_id)
                    <a href="https://recland.co/admin/job/{{ $recland_job_id }}/edit" target="_blank"
                        class="btn btn-success btn-lg float-end me-2 mt-3"><i class="la la-cloud-upload"></i>Xem job
                        trên Recland
                    </a>
                    @else
                    <button type="button" class="btn btn-success btn-lg float-end me-2 mt-3"
                        id="push-job-to-recland-btn"><i class="la la-cloud-upload"></i>Push job to Recland
                    </button>
                    @endif
                </div>

            </div>

            <div class="col-12 pt-5">
                <div class="card">
                    <div class="card-body">
                        <h2>
                            <center>Thống kê trạng thái ứng tuyển</center>
                        </h2>
                        @if ($reportData && count($reportData))
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th rowspan="2">Job Title</th>
                                    @foreach ($reportData as $groupId => $group)
                                    <th colspan="{{ count($group['statuses']) }}" class="group-header">
                                        {{ $group['name'] }}</th>
                                    @endforeach
                                </tr>
                                <tr>
                                    @foreach ($reportData as $group)
                                    @foreach ($group['statuses'] as $statusId => $status)
                                    <th class="status-header">{{ $status['name'] }}</th>
                                    @endforeach
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                $allJobs = collect($reportData)
                                ->flatMap(function ($group) {
                                return collect($group['statuses'])->flatMap(function ($status) {
                                return $status['jobs'];
                                });
                                })
                                ->unique('title')
                                ->sortBy('title');
                                @endphp

                                @foreach ($allJobs as $jobId => $job)
                                <tr>
                                    <td class="job-title">{{ $job['title'] }}</td>
                                    @foreach ($reportData as $group)
                                    @foreach ($group['statuses'] as $statusId => $status)
                                    <td>
                                        {{ $status['jobs'][$jobId]['count'] ?? 0 }}
                                    </td>
                                    @endforeach
                                    @endforeach
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>Total</th>
                                    @foreach ($reportData as $group)
                                    @foreach ($group['statuses'] as $statusId => $status)
                                    <th>{{ $status['total'] }}</th>
                                    @endforeach
                                    @endforeach
                                </tr>
                            </tfoot>
                        </table>
                        @endif
                    </div>
                </div>
            </div>




            <div class="col-12 pt-5">
                <div class="card">
                    <div class="card-body">
                        <section class="col-12 text-primary">
                            <h1>Danh sách ứng viên đã apply vào job </h1>
                        </section>
                        {!! $filterHtml !!}
                        {{ $dataTable->table(['class' => 'table table-striped table-bordered table-sm dataTable',
                        'width' => '100%'], true) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

{{-- Modal đánh giá tổng quan --}}
<div class="modal fade" id="match-score-modal" tabindex="-1" role="dialog" aria-labelledby="match-score-modal-label"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="match-score-modal-label">
                    <i class="fas fa-chart-line me-2"></i>Đánh giá tổng quan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Điểm tổng quan -->
                <div class="text-center mb-4">
                    <div class="score-circle mx-auto mb-3" id="overall-score-circle">
                        <span id="overall-score-text">65%</span>
                        <small class="d-block text-muted">Phù hợp</small>
                    </div>
                    <p class="text-muted" id="overall-description">
                        Ứng viên có kỹ năng và kinh nghiệm bán hàng cơ bản, nhưng cần cải thiện trong lĩnh vực chăm sóc
                        khách hàng và quản lý thông tin khách hàng để phù hợp hơn với yêu cầu công việc.
                    </p>
                </div>

                <!-- Chi tiết kỹ năng và kinh nghiệm -->
                <div class="row">
                    <!-- Kỹ năng -->
                    <div class="col-md-6">
                        <div class="evaluation-section">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">Kỹ năng</h6>
                                <span class="badge bg-warning text-dark" id="skills-score">70%</span>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-check-circle me-1"></i>Điểm mạnh
                                </h6>
                                <ul class="list-unstyled" id="skills-advantages">
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                </ul>
                            </div>

                            <div>
                                <h6 class="text-danger mb-2">
                                    <i class="fas fa-times-circle me-1"></i>Điểm cần cải thiện
                                </h6>
                                <ul class="list-unstyled" id="skills-disadvantages">
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Kinh nghiệm -->
                    <div class="col-md-6">
                        <div class="evaluation-section">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="mb-0 me-2">Kinh nghiệm</h6>
                                <span class="badge bg-warning text-dark" id="experience-score">60%</span>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-check-circle me-1"></i>Điểm mạnh
                                </h6>
                                <ul class="list-unstyled" id="experience-advantages">
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                </ul>
                            </div>

                            <div>
                                <h6 class="text-danger mb-2">
                                    <i class="fas fa-times-circle me-1"></i>Điểm cần cải thiện
                                </h6>
                                <ul class="list-unstyled" id="experience-disadvantages">
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                    <li class="mb-1">...</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section('before_breadcrumbs_widgets')
<div class="modal modal-blur fade" id="inline-create-dialog" tabindex="0" data-backdrop="static"
    data-bs-backdrop="static" role="dialog" aria-labelledby="inline-create-dialog-label" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">

        <div class="modal-content">
            <form method="post" enctype="multipart/form-data" id="apply-job-form">
                <div class="modal-header">
                    <h5 class="modal-title" id="inline-create-dialog-label">
                        Apply to job
                    </h5>
                    <button type="button" class="btn-close close" data-dismiss="modal" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body bg-light">
                    <input hidden value="{{ $entry->id }}" id="job_id">
                    {{-- <input hidden value="{{ $status->id }}" id="status_id"> --}}
                    <div class="row">
                        <div class="form-group col-md-6">
                            <label for="exampleInputEmail1">Ứng viên</label>
                            <select class="form-control selectric" id="apply-candidate"></select>
                            <span class="text-danger error-apply" id="apply-candidate-error"></span>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="exampleInputEmail1">Cv ứng viên tương ứng </label>
                            <select class="select2 form-control" id="apply-cv"></select>
                            <span class="text-danger error-apply" id="apply-cv-error"></span>
                        </div>
                    </div>

                    <div class="col-sm-12 d-none" id="detail-cv">
                        <div class="row">
                            <div class="col-xs-12 col-sm-6">
                                <p><strong id="cv-job-title">Job title: </strong><span id="cv-job-title-value"></span>
                                </p>
                                <p><strong id="cv-level">Level: </strong><span id="cv-level-value"></span></p>
                                <p><strong id="cv-yoe">Yoe: </strong><span id="cv-yoe-value"></span></p>
                            </div>
                            <div class="col-xs-12 col-sm-6">
                                <p><strong id="cv-salary-expect">Salary expect: </strong><span
                                        id="cv-salary-expect-value"></span></p>
                                <p><strong id="cv-language">Language: </strong><span id="cv-language-value"></span>
                                </p>
                                <p><strong id="cv-skill">Skills: </strong><span id="cv-skill-value"></span></p>
                            </div>
                        </div>
                        <div class="row hidden" id="div-show-private-cv">
                            <div class="col-12">
                                <label>Xem/tải file CV đã che thông tin: <a class="btn btn-danger" href=""
                                        target="_blank">VIEW / DOWNLOAD</a></label>
                            </div>
                        </div>
                        <div class="row hidden" id="div-upload-private-cv">
                            <div class="col-12">
                                <label>Upload file CV đã che thông tin: </label>
                                <input type="file" name="cv-private-file">
                                <span id="upload-loading"></span>
                            </div>
                        </div>
                    </div>
                    <div class="g-col-md-12">
                        <div class="row">
                            <div class="form-group col-md-3">
                                <label for="desired_salary">Mức lương mong muốn <span
                                        class="text-danger">*</span></label>
                                <input type="number" id="desired_salary" class="form-control" name="desired_salary"
                                    required>
                                <span class="text-danger error-apply" id="apply-desired_salary-error"></span>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="date_onboard">Ngày có thể onboard <span class="text-danger">*</span></label>
                                <input type="text" id="date_onboard" class="form-control" name="date_onboard" required>
                                <span class="text-danger error-apply" id="apply-date_onboard-error"></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="university">University <span class="text-danger">*</span></label>
                                <input type="text" id="university" class="form-control" name="university" required>
                                <span class="text-danger error-apply" id="apply-university-error"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="candidate_strengths">Điểm mạnh của ứng viên (tối thiểu 3 dòng) <span
                                class="text-danger">*</span></label>
                        <textarea id="candidate_strengths" class="form-control" name="candidate_strengths"
                            required></textarea>
                        <span class="text-danger error-apply" id="apply-candidate_strengths-error"></span>
                    </div>


                    <div class="form-group">
                        <label for="exampleInputEmail1">Note </label>
                        <textarea id="note" class="form-control" name="note"></textarea>
                        <span class="text-danger error-apply" id="apply-note-error"></span>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelButtonApply" data-dismiss="modal"
                        data-bs-dismiss="modal" aria-label="Close">{{ trans('backpack::crud.cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="saveButton">{{ trans('backpack::crud.save')
                        }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@section('after_scripts')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/dataTables.bootstrap4.min.js"></script>
@endsection
@push('after_scripts')
@vite('resources/js/pages/job_show.js')
<script>
    function showMatchScore(id) {
        // Gọi API để lấy dữ liệu match score
        var url = '{{ route("apply-job.match-score", ":id") }}';
        url = url.replace(':id', id);
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                if (response && response.data) {
                    populateMatchScoreModal(response.data);
                    $('#match-score-modal').modal('show');
                } else {
                    new Noty({
                        type: "error",
                        text: "Không thể tải dữ liệu đánh giá",
                    }).show();
                }
            },
            error: function(xhr) {
                new Noty({
                    type: "error",
                    text: "Có lỗi xảy ra khi tải dữ liệu",
                }).show();
                console.log(xhr.responseText);
            }
        });
    }
    function showMatchScoreByApplyId(id) {
        // Gọi API để lấy dữ liệu match score
        var url = '{{ route("apply-job.match-score.by-apply-id", ":id") }}';
        url = url.replace(':id', id);
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                if (response && response.data) {
                    populateMatchScoreModal(response.data);
                    $('#match-score-modal').modal('show');
                    $('#btn-create-match-score-' + id).text(response.overview_score + '%');
                } else {
                    new Noty({
                        type: "error",
                        text: "Không thể tải dữ liệu đánh giá",
                    }).show();
                }
            },
            error: function(xhr) {
                new Noty({
                    type: "error",
                    text: "Có lỗi xảy ra khi tải dữ liệu",
                }).show();
                console.log(xhr.responseText);
            }
        });
    }

    function createMatchScore(id) {
        var url = '{{ route("apply-job.create-match-score", ":id") }}';
        url = url.replace(':id', id);
        $('#btn-create-match-score-' + id).prop('disabled', true);
        $('#btn-create-match-score-' + id).text('Đang chấm điểm...');
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                if (response && response.success == 1) {
                    showMatchScoreByApplyId(id);
                    $('#btn-create-match-score-' + id).prop('disabled', false);
                    $('#btn-create-match-score-' + id).text('Chấm điểm');
                }
            },
            error: function(xhr) {
                new Noty({
                    type: "error",
                    text: "Có lỗi xảy ra khi chấm điểm",
                }).show();
                console.log(xhr.responseText);
            }
        });
    }

    function populateMatchScoreModal(data) {
        // Cập nhật điểm tổng quan
        if (data.overview) {
            $('#overall-score-text').text(data.overview.score + '%');
            $('#overall-description').text(data.overview.description || '');
            
            // Cập nhật màu sắc circle theo điểm số
            updateScoreCircleColor('#overall-score-circle', data.overview.score);
        }

        // Cập nhật kỹ năng
        if (data.skills) {
            $('#skills-score').text(data.skills.score + '%');
            updateScoreBadgeColor('#skills-score', data.skills.score);
            
            // Điểm mạnh kỹ năng
            var skillsAdvantagesHtml = '';
            if (data.skills.advantage && data.skills.advantage.length > 0) {
                data.skills.advantage.forEach(function(item) {
                    skillsAdvantagesHtml += '<li class="mb-1">• ' + item + '</li>';
                });
            }
            $('#skills-advantages').html(skillsAdvantagesHtml);

            // Điểm cần cải thiện kỹ năng
            var skillsDisadvantagesHtml = '';
            if (data.skills.disadvantage && data.skills.disadvantage.length > 0) {
                data.skills.disadvantage.forEach(function(item) {
                    skillsDisadvantagesHtml += '<li class="mb-1">• ' + item + '</li>';
                });
            }
            $('#skills-disadvantages').html(skillsDisadvantagesHtml);
        }

        // Cập nhật kinh nghiệm
        if (data.experience) {
            $('#experience-score').text(data.experience.score + '%');
            updateScoreBadgeColor('#experience-score', data.experience.score);
            
            // Điểm mạnh kinh nghiệm
            var experienceAdvantagesHtml = '';
            if (data.experience.advantage && data.experience.advantage.length > 0) {
                data.experience.advantage.forEach(function(item) {
                    experienceAdvantagesHtml += '<li class="mb-1">• ' + item + '</li>';
                });
            }
            $('#experience-advantages').html(experienceAdvantagesHtml);

            // Điểm cần cải thiện kinh nghiệm
            var experienceDisadvantagesHtml = '';
            if (data.experience.disadvantage && data.experience.disadvantage.length > 0) {
                data.experience.disadvantage.forEach(function(item) {
                    experienceDisadvantagesHtml += '<li class="mb-1">• ' + item + '</li>';
                });
            }
            $('#experience-disadvantages').html(experienceDisadvantagesHtml);
        }
    }

    function updateScoreCircleColor(selector, score) {
        var element = $(selector);
        element.removeClass('score-low score-medium score-high');
        
        if (score >= 80) {
            element.addClass('score-high');
        } else if (score >= 60) {
            element.addClass('score-medium');
        } else {
            element.addClass('score-low');
        }
    }

    function updateScoreBadgeColor(selector, score) {
        var element = $(selector);
        element.removeClass('bg-success bg-warning bg-danger');
        
        if (score >= 80) {
            element.addClass('bg-success').removeClass('text-dark').addClass('text-white');
        } else if (score >= 60) {
            element.addClass('bg-warning text-dark');
        } else {
            element.addClass('bg-danger text-white');
        }
    }
        $(document).ready(function() {

            select2('#apply-candidate', "{{ route('candidate.ajax-search') }}", "Chọn ứng viên");
            select2('#apply-cv', "{{ route('cv.ajax-search') }}", "Chọn cv tương ứng");
            $('#apply-cv').prop('disabled', true);
            $('#apply-candidate').on('change', function(e) {
                if ($(this).val() === null) {
                    $('#apply-cv').val('').trigger('change').prop('disabled', true);
                } else {
                    $('#apply-cv').val('').trigger('change').prop('disabled', false);
                }
            })
            $("#inline-create-dialog").on('hide.bs.modal', function() {
                $('#apply-candidate').val('').trigger('change');
                $('#apply-cv').val('').trigger('change').prop('disabled', true);
                $('#error-apply').text('');
            });

            $('#apply-cv').on('change', function() {
                var cvId = $(this).val();
                if (cvId) {
                    $('#detail-cv').removeClass('d-none')
                } else {
                    $('#detail-cv').addClass('d-none')
                }

                var url = '{{ route('api.cv.getCvDetail', ':id') }}';
                url = url.replace(':id', cvId);
                // reset input upload
                $('input[name="cv-private-file"]').val('');

                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(response) {
                        $('#cv-job-title-value').text(response.job);
                        $('#cv-level-value').text(response.level);
                        $('#cv-yoe-value').text(response.yoe);
                        $('#cv-salary-expect-value').text(response.salary_expect);
                        $('#cv-language-value').text(response.language);
                        $('#cv-skill-value').text(response.skill);

                        if (response.cv_private_url != '' && response.cv_private_url != null) {
                            $('#div-show-private-cv').removeClass('hidden');
                            $('#div-show-private-cv a').attr('href', response.cv_private_url);
                            $('#div-upload-private-cv').addClass('hidden');
                        } else {
                            $('#div-show-private-cv').addClass('hidden');
                            $('#div-upload-private-cv').removeClass('hidden');
                        }

                    },
                    error: function(xhr) {
                        console.log(xhr.responseText);
                    }
                });
            });

            // upload file when #cv-private-file change
            $('input[name="cv-private-file"]').on('change', function(e) {
                var cv_id = $('#apply-cv').val();
                var file = e.target.files[0];
                var formData = new FormData();
                formData.append('cv_file', file);
                formData.append('cv_id', cv_id);
                formData.append('type', 'private');
                url = '{{ route('api.cvs.upload-cv-file', ':id') }}';
                url = url.replace(':id', cv_id);
                $('#upload-loading').text('Uploading...');
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if (response.status == 'success') {
                            $('#div-show-private-cv').removeClass('hidden');
                            $('#div-show-private-cv a').attr('href', response.data
                                .cv_private_url);
                            $('#div-upload-private-cv').addClass('hidden');
                        }
                        $('#upload-loading').text('');
                    },
                    error: function(xhr) {
                        $('#upload-loading').text('');
                        alert('Upload file failed');
                        console.log(xhr.responseText);
                    }
                });
            });




            $('#apply-job-form').on('submit', function(e) {
                $('#error-apply').text('');
                $('#apply-cv-error').text('');
                if ($('#div-show-private-cv').hasClass('hidden')) {
                    $('#apply-cv-error').text('Vui lòng upload file CV đã che thông tin');
                    new Noty({
                        type: "error",
                        text: 'Vui lòng upload file CV đã che thông tin',
                    }).show();
                    e.preventDefault();
                    return;
                }

                //validate "nêu điểm mạnh candidate (3 dòng)"
                var candidateStrengths = $('#candidate_strengths').val();
                var lines = candidateStrengths.split('\n');
                lines = lines.filter(function(line) {
                    return line.trim() !== '';
                });
                if (lines.length < 3) {
                    $('#apply-candidate_strengths-error').text('Vui lòng nhập ít nhất 3 dòng.');
                    new Noty({
                        type: "error",
                        text: 'Vui lòng nhập ít nhất 3 dòng',
                    }).show();
                    e.preventDefault();
                    return;
                }

                $.ajax({
                    type: "POST",
                    url: "{{ route('apply-job.store-ajax') }}",
                    data: {
                        job_id: $('#job_id').val(),
                        candidate_id: $('#apply-candidate').val(),
                        cv_id: $('#apply-cv').val(),
                        note: $('#note').val(),
                        candidate_strengths: $('#candidate_strengths').val(),
                        date_onboard: $('#date_onboard').val(),
                        desired_salary: $('#desired_salary').val(),
                        university: $('#university').val(),
                    },
                    dataType: "json",
                    encode: true,
                }).done(function(data) {
                    $('#dataTableBuilder').DataTable().ajax.reload();
                    new Noty({
                        type: "success",
                        text: "Apply job success",
                    }).show();
                    $('#cancelButtonApply').click();

                }).error(function(res) {
                    if (res.responseJSON && res.responseJSON.errors && res.responseJSON.errors
                        .candidate_id) {
                        $('#apply-candidate-error').text(res.responseJSON.errors.candidate_id[0]);
                    } else if (res.responseJSON && res.responseJSON.errors && res.responseJSON
                        .errors.cv_id) {
                        $('#apply-cv-error').text(res.responseJSON.errors.cv_id[0]);
                    } else {
                        new Noty({
                            type: "warning",
                            text: e.detail[0].message,
                        }).show();
                    }
                });

                e.preventDefault();
            });
        });

        function select2(element, url, placeholder) {
            $(element).select2({
                placeholder: placeholder,
                allowClear: true,
                dropdownParent: $('#inline-create-dialog'),
                width: '100%',
                dropdownCssClass: "custom-select2-dropdown",
                ajax: {
                    url: url,
                    delay: 250,
                    data: function data(params) {

                        return {
                            keyword: params.term,
                            candidate_id: $('#apply-candidate').val(),
                        };
                    },
                    processResults: function processResults(response) {
                        var output = {};
                        output.results = response.map(function(item) {
                            return {
                                "id": item.id,
                                "text": item.name,
                                "selected": false
                            };
                        });
                        return output;
                    },
                    global: false
                }
            });
        }
</script>

<!-- Vue App Container for Modal -->
<div id="app-push-job-to-recland">
    <push-job-to-recland-modal :show="showPushModal" :job-id="{{ $entry->id }}" :max-salary="{{ $entry->max_salary }}"
        :salary-currency="{{ $entry->salary_currency }}" @close="showPushModal = false" @success="handleSuccess">
    </push-job-to-recland-modal>
</div>
<script>
    // Vue app for Push Job to Recland Modal
// const { createApp } = Vue;


// Chỉ mount app nếu element tồn tại
// if (document.getElementById('app')) {
//     pushJobApp.mount('#app-push-job-to-recland');
// }
</script>

{{ $dataTable->scripts() }}
@endpush

@section('before_styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{{--
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css"> --}}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css">
<style>
    .select2-selection--single {
        font-size: 1.5em !important;
        height: 41px !important;
        overflow: auto !important;
        padding-top: 4px
    }

    .select2-results__options {
        font-size: 1.5rem !important;
    }

    .select2-selection__arrow {
        top: 8px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__clear {
        margin-top: -2px;
    }

    .pagination {
        float: right;
    }

    /* Match Score Modal Styles */
    .score-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        border: 4px solid;
        position: relative;
    }

    .score-circle.score-high {
        border-color: #28a745;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.3) 100%);
        color: #28a745;
    }

    .score-circle.score-medium {
        border-color: #ffc107;
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.3) 100%);
        color: #856404;
    }

    .score-circle.score-low {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.3) 100%);
        color: #dc3545;
    }

    .evaluation-section {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        background: #f8f9fa;
        height: 100%;
    }

    #match-score-modal .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    #match-score-modal .list-unstyled li {
        padding: 0.25rem 0;
        line-height: 1.4;
    }

    #match-score-modal .badge {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .text-success i {
        color: #28a745 !important;
    }

    .text-danger i {
        color: #dc3545 !important;
    }
</style>
@endsection