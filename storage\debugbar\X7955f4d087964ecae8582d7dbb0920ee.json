{"__meta": {"id": "X7955f4d087964ecae8582d7dbb0920ee", "datetime": "2025-07-29 09:57:43", "utime": 1753757863.4908, "method": "POST", "uri": "/admin/apply-job/search?", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.303032, "end": 1753757863.490817, "duration": 1.1877851486206055, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": **********.303032, "relative_start": 0, "end": **********.618666, "relative_end": **********.618666, "duration": 0.31563401222229004, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.618677, "relative_start": 0.3156449794769287, "end": 1753757863.490819, "relative_end": 1.9073486328125e-06, "duration": 0.8721420764923096, "duration_str": "872ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51473696, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 180, "templates": [{"name": "10x crud::columns.row_number", "param_count": null, "params": [], "start": **********.907964, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/row_number.blade.phpcrud::columns.row_number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Frow_number.blade.php&line=1", "ajax": false, "filename": "row_number.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::columns.row_number"}, {"name": "10x crud::columns.text ", "param_count": null, "params": [], "start": **********.963915, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.phpcrud::columns.text ", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::columns.text "}, {"name": "80x crud::columns.custom_html", "param_count": null, "params": [], "start": **********.966352, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.phpcrud::columns.custom_html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Fcustom_html.blade.php&line=1", "ajax": false, "filename": "custom_html.blade.php", "line": "?"}, "render_count": 80, "name_original": "crud::columns.custom_html"}, {"name": "10x crud::columns.text", "param_count": null, "params": [], "start": 1753757863.027184, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.phpcrud::columns.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::columns.text"}, {"name": "20x crud::columns.closure", "param_count": null, "params": [], "start": 1753757863.13448, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.phpcrud::columns.closure", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Fclosure.blade.php&line=1", "ajax": false, "filename": "closure.blade.php", "line": "?"}, "render_count": 20, "name_original": "crud::columns.closure"}, {"name": "10x crud::columns.date", "param_count": null, "params": [], "start": 1753757863.203143, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/date.blade.phpcrud::columns.date", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fcolumns%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::columns.date"}, {"name": "10x crud::inc.button_stack", "param_count": null, "params": [], "start": 1753757863.264563, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::inc.button_stack"}, {"name": "10x crud::buttons.show", "param_count": null, "params": [], "start": 1753757863.266026, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/buttons/show.blade.phpcrud::buttons.show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fbuttons%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::buttons.show"}, {"name": "10x crud::buttons.update", "param_count": null, "params": [], "start": 1753757863.267503, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/buttons/update.blade.phpcrud::buttons.update", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fbuttons%2Fupdate.blade.php&line=1", "ajax": false, "filename": "update.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::buttons.update"}, {"name": "10x crud::buttons.delete", "param_count": null, "params": [], "start": 1753757863.26879, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/buttons/delete.blade.phpcrud::buttons.delete", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Fbuttons%2Fdelete.blade.php&line=1", "ajax": false, "filename": "delete.blade.php", "line": "?"}, "render_count": 10, "name_original": "crud::buttons.delete"}]}, "route": {"uri": "POST admin/apply-job/search", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin, Closure", "as": "apply-job.search", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\ApplyJobCrudController@search", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=72\" onclick=\"\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:72-120</a>"}, "queries": {"nb_statements": 134, "nb_visible_statements": 136, "nb_excluded_statements": -1, "nb_failed_statements": 0, "accumulated_duration": 0.13082999999999992, "accumulated_duration_str": "131ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 34 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.672698, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.687358, "duration": 0.02571, "duration_str": "25.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 19.651}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.786168, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 19.651, "width_percent": 0.474}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.788748, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 20.125, "width_percent": 0.436}, {"sql": "select `company_abbreviation`, `id` from `companies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 403}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 396}], "start": **********.7975178, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:403", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=403", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "403"}, "connection": "c_hri", "explain": null, "start_percent": 20.561, "width_percent": 0.466}, {"sql": "select distinct `created_by` from `jobs` where `jobs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 418}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 411}], "start": **********.800329, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:418", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=418", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "418"}, "connection": "c_hri", "explain": null, "start_percent": 21.027, "width_percent": 0.696}, {"sql": "select `email`, `id` from `users` where `id` in (19, 70, 20, 30, 25, 45, 22, 21, 5, 46, 69, 67, 1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [19, 70, 20, 30, 25, 45, 22, 21, 5, 46, 69, 67, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 419}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 411}], "start": **********.802373, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:419", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 419}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=419", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "419"}, "connection": "c_hri", "explain": null, "start_percent": 21.723, "width_percent": 0.405}, {"sql": "select distinct `created_by` from `apply_jobs`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 442}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 435}], "start": **********.804517, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:442", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=442", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "442"}, "connection": "c_hri", "explain": null, "start_percent": 22.128, "width_percent": 1.819}, {"sql": "select `email`, `id` from `users` where `id` in (1, 20, 28, 33, 22, 18, 24, 15, 37, 17, 42, 43, 29, 34, 38, 36, 45, 47, 25, 48, 49, 52, 54, 59, 57, 61, 56, 58, 60, 62, 64, 66, 65) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 20, 28, 33, 22, 18, 24, 15, 37, 17, 42, 43, 29, 34, 38, 36, 45, 47, 25, 48, 49, 52, 54, 59, 57, 61, 56, 58, 60, 62, 64, 66, 65], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 443}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 435}], "start": **********.808036, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:443", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 443}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=443", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "443"}, "connection": "c_hri", "explain": null, "start_percent": 23.947, "width_percent": 0.474}, {"sql": "select `title`, `id` from `jobs` where `jobs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 466}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 459}], "start": **********.809879, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:466", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 466}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=466", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "466"}, "connection": "c_hri", "explain": null, "start_percent": 24.421, "width_percent": 0.795}, {"sql": "select `name`, `id` from `status` where `group` = 'apply-job' and `parent_id` is null", "type": "query", "params": [], "bindings": ["apply-job"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 482}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 475}], "start": **********.81253, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:482", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=482", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "482"}, "connection": "c_hri", "explain": null, "start_percent": 25.216, "width_percent": 0.375}, {"sql": "select `name`, `id` from `status` where `group` = 'job-type' and `parent_id` is null", "type": "query", "params": [], "bindings": ["job-type"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 497}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudFilter.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudFilter.php", "line": 63}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 84}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Filters.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Filters.php", "line": 54}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 490}], "start": **********.814184, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:497", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 497}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=497", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "497"}, "connection": "c_hri", "explain": null, "start_percent": 25.59, "width_percent": 0.405}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'apply_jobs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 332}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 384}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 343}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 277}], "start": **********.820439, "duration": 0.024149999999999998, "duration_str": "24.15ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:332", "source": {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 332}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=332", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "332"}, "connection": "c_hri", "explain": null, "start_percent": 25.996, "width_percent": 18.459}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'jobs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.847667, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 44.455, "width_percent": 3.195}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'jobs' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.8531249, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 47.65, "width_percent": 0.734}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'status' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.855665, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 48.383, "width_percent": 2.736}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'status' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.860265, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 51.12, "width_percent": 0.688}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'candidates' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.862994, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 51.808, "width_percent": 3.126}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'candidates' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.8681068, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 54.934, "width_percent": 0.703}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'cvs' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.871095, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 55.637, "width_percent": 3.226}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'cvs' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.87637, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 58.863, "width_percent": 0.703}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.8785658, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:78", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=78", "ajax": false, "filename": "DatabaseSchema.php", "line": "78"}, "connection": "c_hri", "explain": null, "start_percent": 59.566, "width_percent": 3.554}, {"sql": "select index_name as `name`, group_concat(column_name order by seq_in_index) as `columns`, index_type as `type`, not non_unique as `unique` from information_schema.statistics where table_schema = 'c_hri' and table_name = 'users' group by index_name, index_type, non_unique", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 69}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Models/Traits/HasIdentifiableAttribute.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Models\\Traits\\HasIdentifiableAttribute.php", "line": 40}], "start": **********.884273, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseSchema.php:92", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/Database/DatabaseSchema.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\Database\\DatabaseSchema.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FDatabase%2FDatabaseSchema.php&line=92", "ajax": false, "filename": "DatabaseSchema.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 63.12, "width_percent": 0.696}, {"sql": "select * from `apply_jobs` order by `id` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.889665, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 63.816, "width_percent": 0.283}, {"sql": "select * from `jobs` where `jobs`.`id` in (233, 248, 263, 268, 298) and `jobs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8930779, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 64.098, "width_percent": 0.321}, {"sql": "select * from `status` where `status`.`id` in (26, 33, 81)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.894881, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 64.419, "width_percent": 0.214}, {"sql": "select * from `candidates` where `candidates`.`id` in (22167, 96519, 96522, 96524, 96525, 96526, 96528, 96530, 96532, 96533)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8962889, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 64.633, "width_percent": 0.718}, {"sql": "select * from `cvs` where `cvs`.`id` in (115114, 115117, 115118, 115120, 115121, 115123, 115125, 115128, 115129, 115130) and `cvs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.898792, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 65.352, "width_percent": 0.413}, {"sql": "select * from `users` where `users`.`id` in (24, 54, 57, 60, 64) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.900966, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Read.php:198", "source": {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=198", "ajax": false, "filename": "Read.php", "line": "198"}, "connection": "c_hri", "explain": null, "start_percent": 65.765, "width_percent": 0.26}, {"sql": "select count(*) as total_rows from (select `apply_jobs`.`id` from `apply_jobs`) as `apply_jobs_aggregator`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 287}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 207}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 109}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.90476, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Query.php:287", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FQuery.php&line=287", "ajax": false, "filename": "Query.php", "line": "287"}, "connection": "c_hri", "explain": null, "start_percent": 66.025, "width_percent": 0.604}, {"sql": "select * from `companies` where `companies`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, {"index": 23, "namespace": "view", "name": "crud::columns.text", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.php", "line": 11}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.964468, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:99", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=99", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "99"}, "connection": "c_hri", "explain": null, "start_percent": 66.628, "width_percent": 0.222}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.0177178, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 66.85, "width_percent": 0.466}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.023413, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 67.316, "width_percent": 1.208}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.028019, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 68.524, "width_percent": 0.313}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115121, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.030231, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 68.837, "width_percent": 0.268}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.123421, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 69.105, "width_percent": 0.581}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115121, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.127121, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 69.686, "width_percent": 0.428}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.130216, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 70.114, "width_percent": 0.42}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115121, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.13193, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 70.534, "width_percent": 0.336}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.19648, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 70.871, "width_percent": 0.405}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1890 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1890], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.200246, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 71.276, "width_percent": 0.78}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.275419, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 72.055, "width_percent": 0.359}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115125", "type": "query", "params": [], "bindings": [115125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.280165, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 72.415, "width_percent": 0.436}, {"sql": "select * from `cvs` where `cvs`.`id` = 115125 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.2862709, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 72.85, "width_percent": 0.443}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115125, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115125, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.2894468, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 73.294, "width_percent": 0.321}, {"sql": "select * from `cvs` where `cvs`.`id` = 115125 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.292084, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 73.615, "width_percent": 0.436}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115125, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115125, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.29541, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 74.05, "width_percent": 0.313}, {"sql": "select * from `cvs` where `cvs`.`id` = 115125 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115125], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.297453, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 74.364, "width_percent": 0.558}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115125, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115125, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.300768, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 74.922, "width_percent": 0.397}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.304207, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 75.319, "width_percent": 0.382}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1889 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1889], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.306684, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 75.701, "width_percent": 0.42}, {"sql": "select * from `companies` where `companies`.`id` = 91 limit 1", "type": "query", "params": [], "bindings": [91], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, {"index": 23, "namespace": "view", "name": "crud::columns.text", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.php", "line": 11}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.316345, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:99", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=99", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "99"}, "connection": "c_hri", "explain": null, "start_percent": 76.122, "width_percent": 0.39}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.319148, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 76.512, "width_percent": 0.344}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115130", "type": "query", "params": [], "bindings": [115130], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.322039, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 76.855, "width_percent": 0.428}, {"sql": "select * from `cvs` where `cvs`.`id` = 115130 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115130], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.3259308, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 77.283, "width_percent": 0.321}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115130, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115130, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.32785, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 77.605, "width_percent": 0.229}, {"sql": "select * from `cvs` where `cvs`.`id` = 115130 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115130], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.329488, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 77.834, "width_percent": 0.306}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115130, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115130, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.3314312, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 78.14, "width_percent": 0.237}, {"sql": "select * from `cvs` where `cvs`.`id` = 115130 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115130], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.3331459, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 78.377, "width_percent": 0.306}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115130, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115130, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.335071, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 78.682, "width_percent": 0.237}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.337496, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 78.919, "width_percent": 0.229}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1888 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1888], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.339565, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 79.149, "width_percent": 0.26}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.3463311, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 79.408, "width_percent": 0.229}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115129", "type": "query", "params": [], "bindings": [115129], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.34917, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 79.638, "width_percent": 0.298}, {"sql": "select * from `cvs` where `cvs`.`id` = 115129 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115129], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.3529441, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 79.936, "width_percent": 0.375}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115129, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115129, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.356092, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 80.31, "width_percent": 0.229}, {"sql": "select * from `cvs` where `cvs`.`id` = 115129 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115129], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.357494, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 80.54, "width_percent": 0.252}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115129, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115129, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.3589609, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 80.792, "width_percent": 0.199}, {"sql": "select * from `cvs` where `cvs`.`id` = 115129 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115129], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.360321, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 80.991, "width_percent": 0.252}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115129, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115129, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.3617978, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 81.243, "width_percent": 0.191}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.36375, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 81.434, "width_percent": 0.191}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1887 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1887], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.365468, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 81.625, "width_percent": 0.206}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.370731, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 81.831, "width_percent": 0.222}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115118", "type": "query", "params": [], "bindings": [115118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.373101, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 82.053, "width_percent": 0.245}, {"sql": "select * from `cvs` where `cvs`.`id` = 115118 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.376036, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 82.298, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115118, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115118, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.377514, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 82.558, "width_percent": 0.199}, {"sql": "select * from `cvs` where `cvs`.`id` = 115118 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.378849, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 82.756, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115118, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115118, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.3803468, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 83.016, "width_percent": 0.199}, {"sql": "select * from `cvs` where `cvs`.`id` = 115118 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.381705, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 83.215, "width_percent": 0.252}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115118, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115118, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.3831909, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 83.467, "width_percent": 0.191}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.385194, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 83.658, "width_percent": 0.206}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1886 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1886], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.3869941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 83.865, "width_percent": 0.214}, {"sql": "select * from `companies` where `companies`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, {"index": 23, "namespace": "view", "name": "crud::columns.text", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.php", "line": 11}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.391498, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:99", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=99", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "99"}, "connection": "c_hri", "explain": null, "start_percent": 84.079, "width_percent": 0.206}, {"sql": "select * from `status` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.3935568, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 84.285, "width_percent": 0.191}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115128", "type": "query", "params": [], "bindings": [115128], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.395708, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 84.476, "width_percent": 0.252}, {"sql": "select * from `cvs` where `cvs`.`id` = 115128 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115128], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.398578, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 84.728, "width_percent": 0.268}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115128, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115128, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.400147, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 84.996, "width_percent": 0.443}, {"sql": "select * from `cvs` where `cvs`.`id` = 115128 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115128], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.40185, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 85.439, "width_percent": 0.29}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115128, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115128, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.403416, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 85.73, "width_percent": 0.443}, {"sql": "select * from `cvs` where `cvs`.`id` = 115128 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115128], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.405104, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 86.173, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115128, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115128, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.4065852, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 86.433, "width_percent": 0.474}, {"sql": "select * from `status` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.408902, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:229", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=229", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "229"}, "connection": "c_hri", "explain": null, "start_percent": 86.907, "width_percent": 0.199}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = 1885 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [1885], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, {"index": 17, "namespace": "view", "name": "crud::columns.closure", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/closure.blade.php", "line": 10}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.4106438, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:249", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=249", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "249"}, "connection": "c_hri", "explain": null, "start_percent": 87.105, "width_percent": 0.199}, {"sql": "select * from `companies` where `companies`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, {"index": 23, "namespace": "view", "name": "crud::columns.text", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/text.blade.php", "line": 11}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.41503, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:99", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=99", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "99"}, "connection": "c_hri", "explain": null, "start_percent": 87.304, "width_percent": 0.252}, {"sql": "select * from `status` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, {"index": 17, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.4171388, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=130", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "130"}, "connection": "c_hri", "explain": null, "start_percent": 87.556, "width_percent": 0.183}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115123", "type": "query", "params": [], "bindings": [115123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, {"index": 21, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757863.419323, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:164", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=164", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "164"}, "connection": "c_hri", "explain": null, "start_percent": 87.74, "width_percent": 0.313}, {"sql": "select * from `cvs` where `cvs`.`id` = 115123 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 29, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.4253, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 88.053, "width_percent": 0.275}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115123, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115123, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 31, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.426793, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 88.328, "width_percent": 0.405}, {"sql": "select * from `cvs` where `cvs`.`id` = 115123 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.428409, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 88.733, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115123, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:43', '2025-07-29 09:57:43', '2025-07-29 09:57:43')", "type": "query", "params": [], "bindings": [115123, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:43", "2025-07-29 09:57:43", "2025-07-29 09:57:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 203}, {"index": 30, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757863.429913, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 88.993, "width_percent": 0.405}, {"sql": "select * from `cvs` where `cvs`.`id` = 115123 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 204}, {"index": 28, "namespace": "view", "name": "crud::columns.custom_html", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/columns/custom_html.blade.php", "line": 9}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753757863.431632, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 89.398, "width_percent": 0.252}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.433154, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 89.651, "width_percent": 0.39}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.435919, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 90.041, "width_percent": 0.283}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = ? order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.437483, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 90.323, "width_percent": 0.428}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.441948, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 90.751, "width_percent": 0.321}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.443332, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 91.072, "width_percent": 0.428}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.445646, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 91.5, "width_percent": 0.268}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4464571, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 91.768, "width_percent": 0.359}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.447335, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 92.127, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4482331, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 92.387, "width_percent": 0.352}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.449105, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 92.739, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4499092, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 92.999, "width_percent": 0.336}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.451358, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 93.335, "width_percent": 0.206}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = ? order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4522061, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 93.541, "width_percent": 0.214}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.45638, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 93.755, "width_percent": 0.199}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.457608, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 93.954, "width_percent": 0.237}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.459539, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 94.191, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.460351, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 94.451, "width_percent": 0.313}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.461169, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 94.764, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.461963, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 95.024, "width_percent": 0.298}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.462776, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 95.322, "width_percent": 0.252}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.463551, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 95.574, "width_percent": 0.336}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4650068, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 95.911, "width_percent": 0.199}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = ? order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.465826, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 96.109, "width_percent": 0.214}, {"sql": "select * from `companies` where `companies`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.469315, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 96.323, "width_percent": 0.206}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.470558, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 96.53, "width_percent": 0.191}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.471779, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 96.721, "width_percent": 0.245}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.473744, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 96.966, "width_percent": 0.26}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.474553, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 97.225, "width_percent": 0.367}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.475461, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 97.592, "width_percent": 0.42}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.4764822, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 98.013, "width_percent": 0.482}, {"sql": "select * from `cvs` where `cvs`.`id` = ? and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.477517, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 98.494, "width_percent": 0.42}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.478516, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 98.915, "width_percent": 0.482}, {"sql": "select * from `status` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.480166, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 99.396, "width_percent": 0.298}, {"sql": "select * from `log_change_status_applies` where `apply_job_id` = ? order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753757863.481099, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "c_hri", "explain": null, "start_percent": 99.694, "width_percent": 0.306}]}, "models": {"data": {"App\\Models\\Cv": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}, "App\\Models\\Status": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FStatus.php&line=1", "ajax": false, "filename": "Status.php", "line": "?"}}, "App\\Models\\CareerLanguage": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLanguage.php&line=1", "ajax": false, "filename": "CareerLanguage.php", "line": "?"}}, "App\\Models\\ApplyJob": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\Candidate": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCandidate.php&line=1", "ajax": false, "filename": "Candidate.php", "line": "?"}}, "App\\Models\\LogChangeStatusApply": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FLogChangeStatusApply.php&line=1", "ajax": false, "filename": "LogChangeStatusApply.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Job": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Company": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 124, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793663, "xdebug_link": null}, {"message": "[\n  ability => apply-job.edit,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-793239913 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793239913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794777, "xdebug_link": null}, {"message": "[\n  ability => apply-job.show,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1504021749 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.show </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.show</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504021749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.795489, "xdebug_link": null}]}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/apply-job/search", "status_code": "<pre class=sf-dump id=sf-dump-1938247975 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1938247975\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1524572764 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1524572764\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1530887044 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>9</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>totalEntryCount</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530887044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3235</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">http://chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImYxZloxdnV1Vjd1N1hBdklhVVhEU1E9PSIsInZhbHVlIjoiSjdlZjI1M3laTTVHY05OVmpLT0tnWE1kS2I2YzZUblFxNFF2MmZhZWNHSyticmVkTUYvVlhIS1U2dk9yak9DOGlMTktXZnVvdGZVTHBOaXUySUQ2VEI2NGNvUHBTd2p6M2lEUElzcUJFR2swT0IxVjJCM3k0ZldJVmFEdDlkT3YiLCJtYWMiOiI0YjM3ZDQ5YTE5MDk2ZTY5YzJjODVkM2E2MWFmNDU5YWUzMzQyZmNkOWJiOWVjNTYyNDg2YmY2YWQwYTQxNDAxIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IlZxU1VwSGVpUStqWHZXKzZxYnREc0E9PSIsInZhbHVlIjoiTUlSNExhQTJUM0d3SUM3SGhhR210T01nR3VDZks0dkozT3ZSbDJEQTZES2w5NTE5WXdIWk92d1ZENUYrTWlnTGp4VFNLWkc1MXFhdkNpQWtDdHNIQkIzK0tmdEQ3ZkJ5S1BVUXFRSE9FWVYvRXNvUERzYnNUK2NiTHZLOTE4VmIiLCJtYWMiOiJjNWFiOGYxOWRhMDhkZmViNzRlNDIwOWI2MzRjNDQzMzgyMGM1MTE1MGYzMjgxYTM2NTIzMTMwOWUxMzZjNDJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-992254306 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992254306\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1921917305 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFFWmtvUDFUODhGQ1daRUpsUWw4RUE9PSIsInZhbHVlIjoiZDlKYmIrN3Z0a2E2L01wMWk0NWdBM3EwVjU3cGY5MlhhNnUraWxPTlozS3BsNmhDNHUzbTY3UDhpUGxrZU9tbGV0QjJLK2d2dk5HekdRcHFGWWFmN3JNbm1lS2NMZTFvZGFRa3VDU2xNSllYUFdYSzVGMGYyNjFmajZNUmVuUVUiLCJtYWMiOiI5MjlkOTAwMDA4Yzg4YzFlNTk1MGNkOTRlN2JjM2FhY2FjMTY5MTQ3YWQ3NDUzM2M4ZjgxMmNlNzkwZTUyNzdiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6ImpxQnpoamV4aFpHeXZEYk55cFVINlE9PSIsInZhbHVlIjoiV0pwemk2aG5VYzFmSUl1bVhmNnozbTUvV1o4M1FRWUhxemZGU3RUMFM4a1k2YVpmR3d3cHVMRk1KeWw0MGxWM1ZtaFc1aGdoaE1ycllLbUkzWFAwdnAreVczTFQ5NUVkYSthQnBqemNJbzUyTFVkdFFMMmZnbElnUkVpV0UwcnEiLCJtYWMiOiI3MjhiMDdmNjY2NDRlNWEwZDY1MWI4NTkzNjAzOTZiMzQyYTFlNWE3M2E2ZWM1YTcxY2VjOGNhYTE0YTMyMDA4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFFWmtvUDFUODhGQ1daRUpsUWw4RUE9PSIsInZhbHVlIjoiZDlKYmIrN3Z0a2E2L01wMWk0NWdBM3EwVjU3cGY5MlhhNnUraWxPTlozS3BsNmhDNHUzbTY3UDhpUGxrZU9tbGV0QjJLK2d2dk5HekdRcHFGWWFmN3JNbm1lS2NMZTFvZGFRa3VDU2xNSllYUFdYSzVGMGYyNjFmajZNUmVuUVUiLCJtYWMiOiI5MjlkOTAwMDA4Yzg4YzFlNTk1MGNkOTRlN2JjM2FhY2FjMTY5MTQ3YWQ3NDUzM2M4ZjgxMmNlNzkwZTUyNzdiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6ImpxQnpoamV4aFpHeXZEYk55cFVINlE9PSIsInZhbHVlIjoiV0pwemk2aG5VYzFmSUl1bVhmNnozbTUvV1o4M1FRWUhxemZGU3RUMFM4a1k2YVpmR3d3cHVMRk1KeWw0MGxWM1ZtaFc1aGdoaE1ycllLbUkzWFAwdnAreVczTFQ5NUVkYSthQnBqemNJbzUyTFVkdFFMMmZnbElnUkVpV0UwcnEiLCJtYWMiOiI3MjhiMDdmNjY2NDRlNWEwZDY1MWI4NTkzNjAzOTZiMzQyYTFlNWE3M2E2ZWM1YTcxY2VjOGNhYTE0YTMyMDA4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921917305\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-97878665 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97878665\", {\"maxDepth\":0})</script>\n"}}