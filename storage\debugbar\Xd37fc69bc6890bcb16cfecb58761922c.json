{"__meta": {"id": "Xd37fc69bc6890bcb16cfecb58761922c", "datetime": "2025-07-29 09:57:36", "utime": **********.902829, "method": "GET", "uri": "/api/created-cv-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.191943, "end": **********.902849, "duration": 0.7109060287475586, "duration_str": "711ms", "measures": [{"label": "Booting", "start": **********.191943, "relative_start": 0, "end": **********.520964, "relative_end": **********.520964, "duration": 0.32902097702026367, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520974, "relative_start": 0.32903099060058594, "end": **********.902852, "relative_end": 3.0994415283203125e-06, "duration": 0.381878137588501, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30352128, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/created-cv-chart", "middleware": "api, web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Api\\Admin\\CvController@getChart", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FApi%2FAdmin%2FCvController.php&line=100\" onclick=\"\">app/Http/Controllers/Api/Admin/CvController.php:100-103</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13044, "accumulated_duration_str": "130ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.580709, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.596118, "duration": 0.02498, "duration_str": "24.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 19.151}, {"sql": "select COUNT(id) as count, DATE_FORMAT(created_at, \"%v\") as formatted_date from `cvs` where `created_at` >= '2024-01-01' and `cvs`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%v\")", "type": "query", "params": [], "bindings": ["2024-01-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CvsRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\CvsRepository.php", "line": 19}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Admin/CvController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\CvController.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.777033, "duration": 0.10546, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "CvsRepository.php:19", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CvsRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\CvsRepository.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FCvsRepository.php&line=19", "ajax": false, "filename": "CvsRepository.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 19.151, "width_percent": 80.849}]}, "models": {"data": {"App\\Models\\Cv": {"value": 52, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 53, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/api/created-cv-chart\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/api/created-cv-chart", "status_code": "<pre class=sf-dump id=sf-dump-884830438 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-884830438\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-519224536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-519224536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-970730489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-970730489\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-196440923 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijd2NUxHeHY2OHRpV29qVXZIekZ0K2c9PSIsInZhbHVlIjoiU1IvemFRajlWZkxHc0dLSmtZWk96bFhjNVM5WGJEckp0bjE5MkN3WVJhTGdsQWJZUFUxTkpra1QyVWRWcTlGLzF1amlVdTNBYjVkL3BkZElvUDZTKzl6TDZ5dmVOYW1BdnJwNW1iaFdhL2Jld3NaYTQ5SnBmbnFvY0IvK1krT2EiLCJtYWMiOiI3MGI3YjRiYzRjNTJjZTRhOTUzZDcyMjk4MjJhMzVmNjRhMjZkNGU3ZTI3MjcxYTNlMDM1NGM1MTU4YTg5YzhkIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImhQbUFVTWdwWWtmWWtTbDJvbEMxaXc9PSIsInZhbHVlIjoiMzJIb2ZmOEE1QmJhbG85dWxjaVZrQVRQc01HWEJEdDNQMWt5bVlWUk1pMXkzOHloa0RrN0w2d1UydWloYkM1cEhVWW1kY0ZZdXdvTEJvbDJLcWZuQ096UzRqeWx4VnRQNXJpTndvUlJ2Z3FnSHZEcnR4VmJKWmhuV1BqNkFyRjgiLCJtYWMiOiIwZDljMDU0YmEyOTk5ZjJiYTk4MmExZTRmNTQyNTEyZWVkNzVkNDU1ZjZhOWIwNzNiYTcwYWExNWZlNGQ1MDg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196440923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-402850233 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402850233\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1225426128 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJwUGtMUXhLWW1abGVDM2xvSjc5RHc9PSIsInZhbHVlIjoiZHppKzRyN0NTTjBGSEVodXVjUlU3RGRLR0lZaHE2cUtsQWk0U3o0c0NnREFFNFBvU3pPL0tzeEw2Ri81REtIT0o3a01WeG5FaWhZOUlKT2VzbU1LMXhhT3dvYm1TNVNxdVliZEhFbk9iNWN1ekFzMlBURjUxY01hdzVqcUZmOU0iLCJtYWMiOiI4MzMwODY1MmJiYjI3ZDY2MDk5NjAwYzE3ODVlNTJhMWFiODcwYjcwYzNjODUwNDU2MzY3MzkxOGQyYWVhMDZhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6ImhiK3ZmTVpMRTcxeCtXSWNhUGxnT3c9PSIsInZhbHVlIjoiV1VLSHpnN242T0FDVnpjRUdDSVpNUTRONmtiMEo3eWhGTTYzOGFkVmZxZFRsdUYzbzFXWGdoQ2NYTVhQQ0J5ZnRNRVN3cU5Xc0V1bzRSeGtud1c5a0pQR1N6bGtqb1hNbU1ITG00UzYwbVBSdUFKVWhkRGVvaUJiY3pmTUZVRGwiLCJtYWMiOiJkZWFjNWI2OGNmMDY1YTQyYmM2ZGRmNDFiZGRlZDA1OGRlZTI5YjYyNDVhMTJlMjQ3OTIzOTI0Y2JiMGIwNTgzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJwUGtMUXhLWW1abGVDM2xvSjc5RHc9PSIsInZhbHVlIjoiZHppKzRyN0NTTjBGSEVodXVjUlU3RGRLR0lZaHE2cUtsQWk0U3o0c0NnREFFNFBvU3pPL0tzeEw2Ri81REtIT0o3a01WeG5FaWhZOUlKT2VzbU1LMXhhT3dvYm1TNVNxdVliZEhFbk9iNWN1ekFzMlBURjUxY01hdzVqcUZmOU0iLCJtYWMiOiI4MzMwODY1MmJiYjI3ZDY2MDk5NjAwYzE3ODVlNTJhMWFiODcwYjcwYzNjODUwNDU2MzY3MzkxOGQyYWVhMDZhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6ImhiK3ZmTVpMRTcxeCtXSWNhUGxnT3c9PSIsInZhbHVlIjoiV1VLSHpnN242T0FDVnpjRUdDSVpNUTRONmtiMEo3eWhGTTYzOGFkVmZxZFRsdUYzbzFXWGdoQ2NYTVhQQ0J5ZnRNRVN3cU5Xc0V1bzRSeGtud1c5a0pQR1N6bGtqb1hNbU1ITG00UzYwbVBSdUFKVWhkRGVvaUJiY3pmTUZVRGwiLCJtYWMiOiJkZWFjNWI2OGNmMDY1YTQyYmM2ZGRmNDFiZGRlZDA1OGRlZTI5YjYyNDVhMTJlMjQ3OTIzOTI0Y2JiMGIwNTgzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225426128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1713752587 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://chri.local/api/created-cv-chart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713752587\", {\"maxDepth\":0})</script>\n"}}