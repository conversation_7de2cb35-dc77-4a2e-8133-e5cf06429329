<?php
$defaultBreadcrumbs = [
trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
$crud->entity_name_plural => url($crud->route),
trans('backpack::crud.preview') => false,
];

// if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
$breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;
?>

<?php $__env->startSection('header'); ?>
<div class="container-fluid d-flex justify-content-between row">
    <section class="header-operation animated fadeIn d-flex mb-2 align-items-baseline d-print-none col-12"
        bp-section="page-header">
        <h1 class="text-capitalize mb-0" bp-section="page-heading"><?php echo $crud->getHeading() ?? $crud->entity_name_plural; ?></h1>
        <p class="ms-2 ml-2 mb-0" bp-section="page-subheading"><?php echo $crud->getSubheading() ??
            mb_ucfirst(trans('backpack::crud.preview')) . ' ' . $crud->entity_name; ?></p>
        <?php if($crud->hasAccess('list')): ?>
        <p class="ms-2 ml-2 mb-0" bp-section="page-subheading-back-button">
            <small><a href="<?php echo e(url($crud->route)); ?>" class="font-sm"><i class="la la-angle-double-left"></i>
                    <?php echo e(trans('backpack::crud.back_to_all')); ?>

                    <span><?php echo e($crud->entity_name_plural); ?></span></a></small>
        </p>
        <?php endif; ?>

    </section>
    <section class="col-12 text-primary">
        <h1> <?php echo e($entry->title); ?> </h1>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row" bp-section="crud-operation-show">
    <div class="<?php echo e($crud->getShowContentClass()); ?>">

        
        <div class="row">
            <?php if($crud->model->translationEnabled()): ?>
            <div class="row">
                <div class="col-md-12 mb-2">
                    
                    <div class="btn-group float-right">
                        <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php echo e(trans('backpack::crud.language')); ?>

                            :
                            <?php echo e($crud->model->getAvailableLocales()[request()->input('_locale') ?
                            request()->input('_locale') : App::getLocale()]); ?>

                            &nbsp; <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <?php $__currentLoopData = $crud->model->getAvailableLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $locale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a class="dropdown-item"
                                href="<?php echo e(url($crud->route . '/' . $entry->getKey() . '/show')); ?>?_locale=<?php echo e($key); ?>"><?php echo e($locale); ?></a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <div class="card card-style1 border-0">
                <div class="card-body ">
                    <div class="row align-items-center ">
                        <div class="col-lg-12 px-xl-10">
                            <div class="bg-danger d-lg-inline-block py-1-9 px-1-9 px-sm-6 mb-1-9 rounded py-2"
                                style="margin-bottom: 15px">

                                <h3 class="h2 text-white mb-0"><?php echo e($entry->cv->name); ?></h3>
                            </div>
                            <ul class="list-unstyled mb-1-9 row">
                                <div class="col-md-4">
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Title:</span><?php echo e($entry->cv->job_title); ?>

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Ngày sinh:</span>
                                        <?php echo e(Carbon\Carbon::parse($entry->cv->dob ? $entry->cv->dob :
                                        $entry->candidate->dob)->format('d/m/Y')); ?>

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Địa điểm:</span>
                                        <?php echo e($entry->cv->address); ?></li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Level:</span>
                                        <?php echo e(optional($entry->cv->careerLevel)->name_vi); ?>

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Trường:</span>
                                        <?php echo e($entry->university); ?>

                                    </li>
                                </div>
                                <div class="col-md-4">

                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Skill:</span>
                                        <?php echo e(implode(', ', $entry->cv->skills->pluck('name')->toArray())); ?>

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Ngày onboard dự
                                            kiến:</span><?php echo e($entry->date_onboard); ?>

                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Ngoại ngữ:</span>
                                        <?php echo e(implode(', ', $entry->cv->careerLanguages->pluck('name_vi')->toArray())); ?>

                                    </li>
                                    <?php if($entry->cv->yoe): ?>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Kinh nghiệm:</span>
                                        <?php echo e($entry->cv->yoe); ?> năm
                                    </li>
                                    <?php endif; ?>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Note:</span> <?php echo e($entry->note); ?>

                                    </li>
                                </div>
                                <div class="col-md-4">
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Mức lương mong muốn:</span>
                                        <?php echo e(number_format($entry->desired_salary)); ?> VND
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">CV public:</span>
                                        <?php if(!empty($entry->cv->url_cv_public)): ?>
                                        <a class="btn btn-primary btn-sm" href="<?php echo e($entry->cv->url_cv_public); ?>"
                                            target="_blank" class="btn btn-sm btn-link">
                                            <span><i class="la la-eye"></i> Xem Cv</span>
                                        </a>
                                        <?php endif; ?>
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">CV private:</span>
                                        <?php
                                        $cvPrivatePath = $entry->cv->url_cv_private;
                                        if (!empty($cvPrivatePath)) {
                                        $cvPrivatePath = gen_url_file_s3($cvPrivatePath);
                                        }
                                        $lasted_cv_private = $entry->getMeta('lasted_cv_private');
                                        if (!empty($lasted_cv_private)) {
                                        $cvPrivatePath = gen_url_file_s3($lasted_cv_private);
                                        }
                                        ?>
                                        <?php if(!empty($cvPrivatePath)): ?>
                                        <a class="btn btn-primary btn-sm" href="<?php echo e($cvPrivatePath); ?>" target="_blank"
                                            class="btn btn-sm btn-link">
                                            <span><i class="la la-eye"></i> Xem Cv</span>
                                        </a>
                                        <a class="btn btn-success btn-sm"
                                            href="<?php echo e(route('download.cv-private.apply-job', ['id' => $entry->id])); ?>">
                                            <i class="la la-download"></i> Tải Cv
                                        </a>
                                        <?php endif; ?>
                                    </li>
                                    <li class="mb-2 mb-xl-3 display-28">

                                        <button type="button" class="btn btn-warning btn-sm"
                                            onclick="$('#updateCvPrivateModal').modal('show');">
                                            <i class="la la-refresh"></i> Cập nhật CV che mới
                                        </button>

                                        <?php
                                        $cvPrivateLogs = $entry->getMeta('cv_private_logs');
                                        $logsCount =
                                        $cvPrivateLogs && count($cvPrivateLogs) > 0
                                        ? count($cvPrivateLogs)
                                        : 0;
                                        ?>
                                        <?php if($logsCount > 0): ?>
                                        <button type="button" class="btn btn-info btn-sm text-white"
                                            onclick="$('#cvPrivateLogsModal').modal('show');">
                                            <i class="la la-history"></i> Lịch sử thay đổi CV (<?php echo e($logsCount); ?>)
                                        </button>
                                        <?php endif; ?>
                                    </li>

                                    <li class="mb-2 mb-xl-3 display-28">
                                        <span class="display-26 me-2 font-weight-600">File đánh giá phỏng vấn:</span>
                                        <?php if(!empty($entry->interview_evaluation_file)): ?>
                                        <a class="btn btn-success btn-sm"
                                            href="<?php echo e(route('apply-job.preview-interview-evaluation', ['id' => $entry->id])); ?>"
                                            target="_blank">
                                            <i class="la la-eye"></i> Xem file đánh giá
                                        </a>
                                        <?php endif; ?>

                                        <button type="button" class="btn btn-primary btn-sm"
                                            onclick="$('#uploadInterviewEvaluationModal').modal('show');">
                                            <i class="la la-upload"></i>
                                            <?php echo e(!empty($entry->interview_evaluation_file) ? 'Cập nhật' : 'Upload'); ?> file
                                            đánh giá
                                        </button>
                                    </li>

                                     
                                        
                                </div>
                                <?php if($entry->candidate_strengths): ?>
                                <div class="col-md-12">
                                    <li class="mb-2 mb-xl-3 display-28"><span
                                            class="display-26  me-2 font-weight-600">Thế
                                            mạnh:</span><?php echo e($entry->candidate_strengths); ?>

                                    </li>
                                </div>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-12" id="app" style="padding: 30px 0!important">
                <status-apply-job apply-job-id="<?php echo e($entry->id); ?>" status-apply="<?php echo e($entry->status); ?>"
                    status-parent="<?php echo e(optional(optional($entry->statusApply)->parent)->id); ?>"></status-apply-job>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('after_scripts'); ?>
<!-- Modal Cập nhật CV che mới -->
<div class="modal fade" id="updateCvPrivateModal" tabindex="-1" role="dialog"
    aria-labelledby="updateCvPrivateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateCvPrivateModalLabel">Cập nhật CV che mới</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                    onclick="$('#updateCvPrivateModal').modal('hide');">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="updateCvPrivateForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="cv_private">File CV che mới</label>
                        <input type="file" class="form-control" id="cv_private" name="cv_private"
                            accept=".pdf,.doc,.docx" required>
                    </div>
                    <div class="form-group">
                        <label for="note">Ghi chú</label>
                        <textarea class="form-control" id="note" name="note" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        onclick="$('#updateCvPrivateModal').modal('hide');">Đóng</button>
                    <button type="submit" class="btn btn-primary" id="submitUpdateCvPrivate">Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Lịch sử thay đổi CV -->
<div class="modal fade" id="cvPrivateLogsModal" tabindex="-1" role="dialog" aria-labelledby="cvPrivateLogsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cvPrivateLogsModalLabel">Lịch sử thay đổi CV</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                    onclick="$('#cvPrivateLogsModal').modal('hide');">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="cvPrivateLogsContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>Đang tải...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"
                    onclick="$('#cvPrivateLogsModal').modal('hide');">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Upload file đánh giá phỏng vấn -->
<div class="modal fade" id="uploadInterviewEvaluationModal" tabindex="-1" role="dialog"
    aria-labelledby="uploadInterviewEvaluationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadInterviewEvaluationModalLabel">Upload file đánh giá kết quả phỏng vấn
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                    onclick="$('#uploadInterviewEvaluationModal').modal('hide');">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="uploadInterviewEvaluationForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="interview_evaluation_file">File đánh giá kết quả phỏng vấn</label>
                        <input type="file" class="form-control" id="interview_evaluation_file"
                            name="interview_evaluation_file" accept=".pdf,.doc,.docx,.xlsx,.xls,.ppt,.pptx" required>
                        <small class="form-text text-muted">Chấp nhận các file: PDF, DOC, DOCX, XLSX, XLS, PPT,
                            PPTX</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        onclick="$('#uploadInterviewEvaluationModal').modal('hide');">Đóng</button>
                    <button type="submit" class="btn btn-primary" id="submitUploadInterviewEvaluation">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>


<?php echo app('Illuminate\Foundation\Vite')('resources/js/app.js'); ?>

<script>
    $(document).ready(function() {
            const applyJobId = <?php echo e($entry->id); ?>;

            // Xử lý form submit cập nhật CV
            $('#updateCvPrivateForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                $.ajax({
                    url: '<?php echo e(url(config('backpack.base.route_prefix'))); ?>/apply-job/' +
                        applyJobId + '/update-cv-private',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function() {
                        $('#submitUpdateCvPrivate').attr('disabled', true).html(
                            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...'
                        );
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Hiển thị thông báo thành công
                            new Noty({
                                type: 'success',
                                text: response.message
                            }).show();

                            // Đóng modal và reset form
                            $('#updateCvPrivateModal').modal('hide');
                            $('#updateCvPrivateForm')[0].reset();

                            // Cập nhật số lượng logs
                            $('.btn-info .la-history').parent().html(
                                '<i class="la la-history"></i> Lịch sử thay đổi CV (' +
                                response.data.logs_count + ')');

                            // Reload trang sau 2 giây
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            // Hiển thị thông báo lỗi
                            new Noty({
                                type: 'error',
                                text: response.message ||
                                    'Có lỗi xảy ra, vui lòng thử lại'
                            }).show();
                        }
                    },
                    error: function(xhr) {
                        // Hiển thị thông báo lỗi
                        const errorMessage = xhr.responseJSON && xhr.responseJSON.message ?
                            xhr.responseJSON.message :
                            'Có lỗi xảy ra, vui lòng thử lại';

                        new Noty({
                            type: 'error',
                            text: errorMessage
                        }).show();
                    },
                    complete: function() {
                        $('#submitUpdateCvPrivate').attr('disabled', false).html('Lưu');
                    }
                });
            });

            // Xử lý hiển thị lịch sử thay đổi CV
            $('#cvPrivateLogsModal').on('shown.bs.modal', function() {
                $.ajax({
                    url: '<?php echo e(url(config('backpack.base.route_prefix'))); ?>/apply-job/' +
                        applyJobId + '/cv-private-logs',
                    type: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            const logs = response.data.logs;
                            let logsHtml = '';

                            if (logs.length > 0) {
                                logsHtml = '<div class="table-responsive">';
                                logsHtml +=
                                    '<table class="table table-bordered table-striped">';
                                logsHtml += '<thead><tr>';
                                logsHtml += '<th>STT</th>';
                                logsHtml += '<th>Thời gian</th>';
                                logsHtml += '<th>Người thực hiện</th>';
                                logsHtml += '<th>CV cũ</th>';
                                logsHtml += '<th>CV mới</th>';
                                logsHtml += '<th>Ghi chú</th>';
                                logsHtml += '</tr></thead>';
                                logsHtml += '<tbody>';

                                logs.forEach((log, index) => {
                                    logsHtml += '<tr>';
                                    logsHtml += '<td>' + (index + 1) + '</td>';
                                    logsHtml += '<td>' + (log.created_at || 'N/A') +
                                        '</td>';
                                    logsHtml += '<td>' + (log.user_name || 'N/A') +
                                        '</td>';
                                    logsHtml += '<td>' + (log.old_path ? '<a href="' +
                                        log.old_path +
                                        '" target="_blank" class="btn btn-sm btn-primary"><i class="la la-eye"></i> Xem</a>' :
                                        'N/A') + '</td>';
                                    logsHtml += '<td>' + (log.new_path ? '<a href="' +
                                        log.new_path +
                                        '" target="_blank" class="btn btn-sm btn-primary"><i class="la la-eye"></i> Xem</a>' :
                                        'N/A') + '</td>';
                                    logsHtml += '<td>' + (log.note || 'N/A') + '</td>';
                                    logsHtml += '</tr>';
                                });

                                logsHtml += '</tbody>';
                                logsHtml += '</table>';
                                logsHtml += '</div>';
                            } else {
                                logsHtml =
                                    '<div class="text-center"><p>Chưa có lịch sử thay đổi CV</p></div>';
                            }

                            $('#cvPrivateLogsContent').html(logsHtml);
                        } else {
                            $('#cvPrivateLogsContent').html(
                                '<div class="text-center"><p>Không thể tải lịch sử thay đổi CV</p></div>'
                            );
                        }
                    },
                    error: function() {
                        $('#cvPrivateLogsContent').html(
                            '<div class="text-center"><p>Có lỗi xảy ra khi tải lịch sử thay đổi CV</p></div>'
                        );
                    }
                });
            });

            // Xử lý form submit upload file đánh giá
            $('#uploadInterviewEvaluationForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                $.ajax({
                    url: '<?php echo e(url(config('backpack.base.route_prefix'))); ?>/apply-job/' +
                        applyJobId + '/upload-interview-evaluation',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function() {
                        $('#submitUploadInterviewEvaluation').attr('disabled', true).html(
                            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...'
                        );
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Hiển thị thông báo thành công
                            new Noty({
                                type: 'success',
                                text: response.message
                            }).show();

                            // Đóng modal và reset form
                            $('#uploadInterviewEvaluationModal').modal('hide');
                            $('#uploadInterviewEvaluationForm')[0].reset();

                            // Reload trang sau 2 giây
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            // Hiển thị thông báo lỗi
                            new Noty({
                                type: 'error',
                                text: response.message ||
                                    'Có lỗi xảy ra, vui lòng thử lại'
                            }).show();
                        }
                    },
                    error: function(xhr) {
                        // Hiển thị thông báo lỗi
                        const errorMessage = xhr.responseJSON && xhr.responseJSON.message ?
                            xhr.responseJSON.message :
                            'Có lỗi xảy ra, vui lòng thử lại';

                        new Noty({
                            type: 'error',
                            text: errorMessage
                        }).show();
                    },
                    complete: function() {
                        $('#submitUploadInterviewEvaluation').attr('disabled', false).html('Upload');
                    }
                });
            });
        });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make(backpack_view('blank'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\crm.hri.com.vn\resources\views/admins/apply_job/show.blade.php ENDPATH**/ ?>