<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Zoha\Metable;

class ApplyJob extends Model implements AuditableContract
{
    use CrudTrait, HasFactory, Auditable, Notifiable, Metable;

    protected $guarded = ['id'];
    
    protected $fillable = [
        'created_by', 'job_id', 'cv_id', 'candidate_id', 'status', 'updated_by', 
        'date_status', 'note', 'desired_salary', 'candidate_strengths', 
        'date_onboard', 'university', 'hide_cv_path', 'interview_evaluation_file'
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->created_by = backpack_user()->id;
        });
        static::created(function ($model) {
            event(new \App\Events\ApplyJobCreatedEvent($model));
            LogChangeStatusApply::create([
                'user_id' => backpack_user()->id,
                'apply_job_id' => $model->id,
                'status_id' => $model->status,
                'note' => $model->note ?? '',
            ]);
        });
        static::updated(function ($model) {
            if ($model->isDirty('status')) {
                // event(new \App\Events\ApplyJobStatusChangedEvent($model));
            }
        });
        static::updating(function ($model) {
            $model->updated_by = backpack_user()->id;
        });
        static::saved(function ($model) {});
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class, 'candidate_id');
    }

    public function job()
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    public function statusApply()
    {
        return $this->belongsTo(Status::class, 'status');
    }

    public function logChangeStatusApply()
    {
        return $this->hasMany(LogChangeStatusApply::class, 'apply_job_id');
    }

    public function matchScoreCv()
    {
        return $this->hasOne(MatchScoreCv::class, 'parent_id')->where('parent_type', ApplyJob::class);
    }

    public function cv()
    {
        return $this->belongsTo(Cv::class, 'cv_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function matchScores()
    {
        return $this->morphMany(MatchScoreCv::class, 'parent');
    }

    public function canViewCv()
    {
        $user = backpack_user();
        if (
            $user->can('apply-job.view-all-public-cv') ||
            $user->can('apply-job.all-data') ||
            $user->can('apply-job.only-team') ||
            $user->can('apply-job.only-user')
        ) {
            return true;
        }


        # nguoi tao job duoc phep xem
        if ($this->job->user_id == backpack_user()->id || $this->job->created_by == backpack_user()->id) {
            return true;
        }

        # check if user is manager of department of apply user
        $department = optional($this->createdBy)->department;
        if ($department) {
            if ($department->manager == backpack_user()->id) {
                return true;
            }
            // if ($department->manager()->where('id', backpack_user()->id)->exists()) {
            //     return true;
            // }
        }
        return false;
    }


    public function routeNotificationForMail($notification)
    {
        $user_ids = [$this->created_by];
        $department = optional($this->createdBy)->department;
        if ($department) {
            $user_ids = array_merge($user_ids, $department->user()->pluck('id')->toArray());
        }
        $recer = $this->job->recerJob;
        if ($recer) {
            $user_ids = array_merge($user_ids, $recer->pluck('id')->toArray());
        }
        $user_ids[] = $this->job->user->id;


        $user = User::whereIn('id', $user_ids)->pluck('name', 'email')->toArray();
        return $user;
    }

    public function scopeOutSide($query)
    {
        $user = backpack_user();
        if (!$user->canOutsideAccess()) {
            return $query->where('created_by', $user->id);
        }
        return $query;
    }

    public function getInterviewEvaluationFileUrlAttribute()
    {
        if (empty($this->interview_evaluation_file)) {
            return null;
        }
        
        return secure_file_url($this->id, 'ApplyJob', 'interview_evaluation_file');
    }
}
