{"__meta": {"id": "X3ebfa3089a7079aac2241e51af29c554", "datetime": "2025-07-29 10:49:20", "utime": **********.25931, "method": "GET", "uri": "/admin/secure-file/download?id=115121&model=Cv&field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753760959.820883, "end": **********.259323, "duration": 0.43843984603881836, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1753760959.820883, "relative_start": 0, "end": **********.136468, "relative_end": **********.136468, "duration": 0.3155848979949951, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.13648, "relative_start": 0.3155970573425293, "end": **********.259324, "relative_end": 1.1920928955078125e-06, "duration": 0.12284398078918457, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31518200, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/secure-file/download", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\FileDownloadController@download", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "where": [], "as": "secure-file.download", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=25\" onclick=\"\">app/Http/Controllers/FileDownloadController.php:25-151</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.033, "accumulated_duration_str": "33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.184771, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.196876, "duration": 0.02454, "duration_str": "24.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 74.364}, {"sql": "insert into `file_access_logs` (`user_id`, `file_path`, `model_type`, `record_id`, `field_name`, `ip_address`, `user_agent`, `hash_token`, `is_successful`, `error_message`, `accessed_at`, `updated_at`, `created_at`) values (null, 'UNKNOWN', 'Cv', '115121', 'cv_private', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c', 0, 'The hash field must be 64 characters.', '2025-07-29 10:49:20', '2025-07-29 10:49:20', '2025-07-29 10:49:20')", "type": "query", "params": [], "bindings": [null, "UNKNOWN", "Cv", "115121", "cv_private", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c", 0, "The hash field must be 64 characters.", "2025-07-29 10:49:20", "2025-07-29 10:49:20", "2025-07-29 10:49:20"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 137}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.239889, "duration": 0.00846, "duration_str": "8.46ms", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:81", "source": {"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=81", "ajax": false, "filename": "FileAccessLog.php", "line": "81"}, "connection": "c_hri", "explain": null, "start_percent": 74.364, "width_percent": 25.636}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/secure-file/download?field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c&id=115121&model=Cv\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/secure-file/download", "status_code": "<pre class=sf-dump id=sf-dump-930534745 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-930534745\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1730510220 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115121</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Cv</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cv_private</span>\"\n  \"<span class=sf-dump-key>hash</span>\" => \"<span class=sf-dump-str title=\"62 characters\">4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730510220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-820969358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-820969358\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-675931708 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IllZTHpHMzVEM0NyMVJabCtWeUJWeWc9PSIsInZhbHVlIjoiYzUvZkJPcXlVSHR5SGtXMWg0ZFdsSWdaV05WbkNZS3phWkdVMW05OUdjWUJyTWIwNWhiT2dOeW9DSzhWQm4vdWZmYURhYWxXR0hWdzgzY1d1WGlmWkxlYW1JSTRuMTdLUGhqZWZsV3NPUEdwTnoxYmk2QVBwU0U1ZWxwcHZrSHMiLCJtYWMiOiJiM2RmNWE0ZGYzZTM5NzNiMDMxYzk3YTQ1YjQ1YWJmZjk5NDE4N2YwMzg3OWNkMDYzYjRmNzhjYWIzOTRhZDdiIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IjFtbFUvZjZDcnpLbHhMOVNvVzgzWWc9PSIsInZhbHVlIjoiUmZyZWJMMmsvTCtFQlMvZk5DNy9xUEMzRjAwcXBFSzluTnlLL2hJMVliMWlUZkNEZVZJMzhjOUFHdTlWSWh4a0o2cVA5dlVnYk1vS0VMd1dSU1Q1andGMzE2b3FYdDdERk5OclpCNDE3MzBDeW52UWk5RVdKTEppN2M0dG92bVEiLCJtYWMiOiI3MzhlOGJhMGQ1MDdjZTFlODZmMDI3NmFiMDFiOTg3MTk2ZGI2YTUxNmFmOTM0ODNhMmRkZjVlOTJjODVkNTMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675931708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222059138 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222059138\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-426527557 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:49:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im0xbzNwQzNzMXpYSnZyQWFycDJLNWc9PSIsInZhbHVlIjoidzFlQll4dU02Mm1LbUxkdHFNVm00VlFEWnJWanBJYkdhbnJvSEJqeTZTcWlxRWU1RHE3T2Y5dTUrVUxOUElmT3AwTS9WTjJvQmFrSkVlMGZCNnM3emhraVJuNzU4d0l6Y1RaQk1vRndxNHVoVWxaSGZIaFlPQ3V5bHJrUW1DTzIiLCJtYWMiOiJlNzFlNTQ5Yjk0YjE1MWRkZWRiY2E0OGE2ZmVjOTAzNmMzYTAzMzY1Y2UzOGRiODU0ODM3ZTQ2MGNmNzM2ZTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IjdwNXlsQU14SmtPNFBFc0x1TjVkc0E9PSIsInZhbHVlIjoiWlQ1MFZscktOcGVhdXY3WTVYWmRESmRkdUdiM3h1OUlrTzBKSjlvbkdQZ0drTE5NeFgxdFVMSDJKVTdIY1poemtnMUNpa3hMUEx5eU4yVHJmaC9WSmxQbEVJRDlNY2tSWmc1UU53akl4bUdGZ0tMeG5GTjd1S2YzK3c1dGZERmMiLCJtYWMiOiIzYmZlZDRiOWJhYTkwNDFiYzg4NTE1N2NiMjdlNTJiYTQwNWM2YzgxNmI4YTlkZjY1YjNkZmQzMjg5ZWNhZGM2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im0xbzNwQzNzMXpYSnZyQWFycDJLNWc9PSIsInZhbHVlIjoidzFlQll4dU02Mm1LbUxkdHFNVm00VlFEWnJWanBJYkdhbnJvSEJqeTZTcWlxRWU1RHE3T2Y5dTUrVUxOUElmT3AwTS9WTjJvQmFrSkVlMGZCNnM3emhraVJuNzU4d0l6Y1RaQk1vRndxNHVoVWxaSGZIaFlPQ3V5bHJrUW1DTzIiLCJtYWMiOiJlNzFlNTQ5Yjk0YjE1MWRkZWRiY2E0OGE2ZmVjOTAzNmMzYTAzMzY1Y2UzOGRiODU0ODM3ZTQ2MGNmNzM2ZTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IjdwNXlsQU14SmtPNFBFc0x1TjVkc0E9PSIsInZhbHVlIjoiWlQ1MFZscktOcGVhdXY3WTVYWmRESmRkdUdiM3h1OUlrTzBKSjlvbkdQZ0drTE5NeFgxdFVMSDJKVTdIY1poemtnMUNpa3hMUEx5eU4yVHJmaC9WSmxQbEVJRDlNY2tSWmc1UU53akl4bUdGZ0tMeG5GTjd1S2YzK3c1dGZERmMiLCJtYWMiOiIzYmZlZDRiOWJhYTkwNDFiYzg4NTE1N2NiMjdlNTJiYTQwNWM2YzgxNmI4YTlkZjY1YjNkZmQzMjg5ZWNhZGM2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426527557\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-408560100 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"148 characters\">http://chri.local/admin/secure-file/download?field=cv_private&amp;hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d268138ecbbbad6c&amp;id=115121&amp;model=Cv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408560100\", {\"maxDepth\":0})</script>\n"}}