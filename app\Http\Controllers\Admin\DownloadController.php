<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Controllers\Controller;
use App\Models\ApplyJob;
use App\Models\Notification;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadController extends Controller
{
    public function applyJobCvPrivate(Request $request)
    {
        $apply = ApplyJob::findOrFail($request->id);

        $candidate_name = optional($apply->cv)->name;
        $job_title = $apply->job->title;

        // Kiểm tra và ưu tiên sử dụng CV trong metadata nếu có
        $cv_private_path = $apply->getMeta('lasted_cv_private');

        if (empty($cv_private_path)) {
            // Nếu không có trong metadata, sử dụng CV private từ model Cv
            if (!$apply->cv || empty($apply->cv->cv_private)) {
                return redirect()->back()->with('error', 'CV not found');
            }
            $cv_private_path = $apply->cv->cv_private;
        }

        # return download file with name is candidate name and job title
        $file_name = 'HRI - ' . Utils::cleanFileName(Utils::shortName($candidate_name)) . ' - ' . Utils::cleanFileName($job_title) . '.pdf';

        // Tạo URL từ đường dẫn S3
        // Tạo signed URL trực tiếp cho download
        if (str_starts_with($cv_private_path, 'http')) {
            $url = $cv_private_path;
        } elseif (file_exists(public_path($cv_private_path))) {
            $url = asset($cv_private_path);
        } else {
            $url = \Storage::disk('s3')->temporaryUrl($cv_private_path, now()->addHour());
        }

        // Sử dụng Guzzle để tải nội dung file
        $client = new Client();
        $response = $client->get($url);
        $content = $response->getBody()->getContents();

        // Trả về response với nội dung file để download
        return response($content)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="' . $file_name . '"');
    }
}
