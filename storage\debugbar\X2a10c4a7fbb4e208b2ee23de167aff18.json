{"__meta": {"id": "X2a10c4a7fbb4e208b2ee23de167aff18", "datetime": "2025-07-29 09:57:46", "utime": 1753757866.286498, "method": "GET", "uri": "/admin/apply-job/1890/show", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.106178, "end": 1753757866.286515, "duration": 1.1803369522094727, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": **********.106178, "relative_start": 0, "end": **********.366618, "relative_end": **********.366618, "duration": 0.2604398727416992, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.366629, "relative_start": 0.2604508399963379, "end": 1753757866.286517, "relative_end": 1.9073486328125e-06, "duration": 0.9198880195617676, "duration_str": "920ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52216144, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 107, "templates": [{"name": "1x admins.apply_job.show", "param_count": null, "params": [], "start": **********.589737, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.phpadmins.apply_job.show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "admins.apply_job.show"}, {"name": "1x backpack.theme-tabler::blank", "param_count": null, "params": [], "start": 1753757866.058812, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/blank.blade.phpbackpack.theme-tabler::blank", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fblank.blade.php&line=1", "ajax": false, "filename": "blank.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::blank"}, {"name": "4x backpack.theme-tabler::inc.widgets", "param_count": null, "params": [], "start": 1753757866.059922, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/widgets.blade.phpbackpack.theme-tabler::inc.widgets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fwidgets.blade.php&line=1", "ajax": false, "filename": "widgets.blade.php", "line": "?"}, "render_count": 4, "name_original": "backpack.theme-tabler::inc.widgets"}, {"name": "1x backpack.ui::widgets.style", "param_count": null, "params": [], "start": 1753757866.062103, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/widgets/style.blade.phpbackpack.ui::widgets.style", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Fwidgets%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::widgets.style"}, {"name": "9x __components::723fe3f0e44fe2b3529303522562360e", "param_count": null, "params": [], "start": 1753757866.116971, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/723fe3f0e44fe2b3529303522562360e.blade.php__components::723fe3f0e44fe2b3529303522562360e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F723fe3f0e44fe2b3529303522562360e.blade.php&line=1", "ajax": false, "filename": "723fe3f0e44fe2b3529303522562360e.blade.php", "line": "?"}, "render_count": 9, "name_original": "__components::723fe3f0e44fe2b3529303522562360e"}, {"name": "1x backpack.theme-tabler::layouts.horizontal_dark", "param_count": null, "params": [], "start": 1753757866.119445, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/horizontal_dark.blade.phpbackpack.theme-tabler::layouts.horizontal_dark", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fhorizontal_dark.blade.php&line=1", "ajax": false, "filename": "horizontal_dark.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.horizontal_dark"}, {"name": "1x backpack.theme-tabler::inc.head", "param_count": null, "params": [], "start": 1753757866.12123, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/head.blade.phpbackpack.theme-tabler::inc.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.head"}, {"name": "1x backpack.theme-tabler::inc.theme_styles", "param_count": null, "params": [], "start": 1753757866.123419, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_styles.blade.phpbackpack.theme-tabler::inc.theme_styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_styles.blade.php&line=1", "ajax": false, "filename": "theme_styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_styles"}, {"name": "1x backpack.ui::inc.styles", "param_count": null, "params": [], "start": 1753757866.131625, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/styles.blade.phpbackpack.ui::inc.styles", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.styles"}, {"name": "1x backpack.theme-tabler::layouts.partials.light_dark_mode_logic", "param_count": null, "params": [], "start": 1753757866.146123, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/light_dark_mode_logic.blade.phpbackpack.theme-tabler::layouts.partials.light_dark_mode_logic", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Flight_dark_mode_logic.blade.php&line=1", "ajax": false, "filename": "light_dark_mode_logic.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.light_dark_mode_logic"}, {"name": "1x backpack.theme-tabler::layouts._horizontal_dark.menu_container", "param_count": null, "params": [], "start": 1753757866.147136, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal_dark/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal_dark.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal_dark%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal_dark.menu_container"}, {"name": "1x backpack.theme-tabler::layouts._horizontal.menu_container", "param_count": null, "params": [], "start": 1753757866.147832, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/_horizontal/menu_container.blade.phpbackpack.theme-tabler::layouts._horizontal.menu_container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2F_horizontal%2Fmenu_container.blade.php&line=1", "ajax": false, "filename": "menu_container.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts._horizontal.menu_container"}, {"name": "2x backpack.theme-tabler::inc.sidebar_content", "param_count": null, "params": [], "start": 1753757866.148747, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/sidebar_content.blade.phpbackpack.theme-tabler::inc.sidebar_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fsidebar_content.blade.php&line=1", "ajax": false, "filename": "sidebar_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.sidebar_content"}, {"name": "2x backpack.ui::inc.menu_items", "param_count": null, "params": [], "start": 1753757866.150117, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/vendor/backpack/ui/inc/menu_items.blade.phpbackpack.ui::inc.menu_items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fui%2Finc%2Fmenu_items.blade.php&line=1", "ajax": false, "filename": "menu_items.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.ui::inc.menu_items"}, {"name": "44x backpack.theme-tabler::components.menu-dropdown-item", "param_count": null, "params": [], "start": 1753757866.155275, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown-item.blade.phpbackpack.theme-tabler::components.menu-dropdown-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown-item.blade.php&line=1", "ajax": false, "filename": "menu-dropdown-item.blade.php", "line": "?"}, "render_count": 44, "name_original": "backpack.theme-tabler::components.menu-dropdown-item"}, {"name": "12x backpack.theme-tabler::components.menu-dropdown", "param_count": null, "params": [], "start": 1753757866.158707, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/components/menu-dropdown.blade.phpbackpack.theme-tabler::components.menu-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Fcomponents%2Fmenu-dropdown.blade.php&line=1", "ajax": false, "filename": "menu-dropdown.blade.php", "line": "?"}, "render_count": 12, "name_original": "backpack.theme-tabler::components.menu-dropdown"}, {"name": "1x backpack.theme-tabler::inc.menu", "param_count": null, "params": [], "start": 1753757866.181909, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu.blade.phpbackpack.theme-tabler::inc.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu"}, {"name": "2x backpack.theme-tabler::inc.topbar_left_content", "param_count": null, "params": [], "start": 1753757866.182664, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_left_content.blade.phpbackpack.theme-tabler::inc.topbar_left_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_left_content.blade.php&line=1", "ajax": false, "filename": "topbar_left_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_left_content"}, {"name": "2x backpack.theme-tabler::layouts.partials.switch_theme", "param_count": null, "params": [], "start": 1753757866.183336, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/switch_theme.blade.phpbackpack.theme-tabler::layouts.partials.switch_theme", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fswitch_theme.blade.php&line=1", "ajax": false, "filename": "switch_theme.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::layouts.partials.switch_theme"}, {"name": "1x backpack.theme-tabler::inc.menu_notification_dropdown", "param_count": null, "params": [], "start": 1753757866.188813, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\resources/views/vendor/backpack/theme-tabler/inc/menu_notification_dropdown.blade.phpbackpack.theme-tabler::inc.menu_notification_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Ftheme-tabler%2Finc%2Fmenu_notification_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_notification_dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.menu_notification_dropdown"}, {"name": "2x backpack.theme-tabler::inc.topbar_right_content", "param_count": null, "params": [], "start": 1753757866.230684, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/topbar_right_content.blade.phpbackpack.theme-tabler::inc.topbar_right_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftopbar_right_content.blade.php&line=1", "ajax": false, "filename": "topbar_right_content.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.topbar_right_content"}, {"name": "2x backpack.theme-tabler::inc.menu_user_dropdown", "param_count": null, "params": [], "start": 1753757866.231329, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/menu_user_dropdown.blade.phpbackpack.theme-tabler::inc.menu_user_dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fmenu_user_dropdown.blade.php&line=1", "ajax": false, "filename": "menu_user_dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "backpack.theme-tabler::inc.menu_user_dropdown"}, {"name": "1x backpack.theme-tabler::layouts.partials.mobile_toggle_btn", "param_count": null, "params": [], "start": 1753757866.237573, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/layouts/partials/mobile_toggle_btn.blade.phpbackpack.theme-tabler::layouts.partials.mobile_toggle_btn", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fmobile_toggle_btn.blade.php&line=1", "ajax": false, "filename": "mobile_toggle_btn.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::layouts.partials.mobile_toggle_btn"}, {"name": "1x backpack.theme-tabler::inc.breadcrumbs", "param_count": null, "params": [], "start": 1753757866.273096, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/breadcrumbs.blade.phpbackpack.theme-tabler::inc.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.breadcrumbs"}, {"name": "1x backpack.theme-tabler::inc.footer", "param_count": null, "params": [], "start": 1753757866.273896, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/footer.blade.phpbackpack.theme-tabler::inc.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.footer"}, {"name": "1x backpack.ui::inc.scripts", "param_count": null, "params": [], "start": 1753757866.275601, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\ui/inc/scripts.blade.phpbackpack.ui::inc.scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fui%2Finc%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.ui::inc.scripts"}, {"name": "7x __components::8a07812e6d8f9d2826754abad88e5380", "param_count": null, "params": [], "start": 1753757866.276652, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\storage\\framework\\views/8a07812e6d8f9d2826754abad88e5380.blade.php__components::8a07812e6d8f9d2826754abad88e5380", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fstorage%2Fframework%2Fviews%2F8a07812e6d8f9d2826754abad88e5380.blade.php&line=1", "ajax": false, "filename": "8a07812e6d8f9d2826754abad88e5380.blade.php", "line": "?"}, "render_count": 7, "name_original": "__components::8a07812e6d8f9d2826754abad88e5380"}, {"name": "1x backpack.theme-tabler::inc.alerts", "param_count": null, "params": [], "start": 1753757866.278773, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/alerts.blade.phpbackpack.theme-tabler::inc.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.alerts"}, {"name": "1x crud::inc.ajax_error_frame", "param_count": null, "params": [], "start": 1753757866.280205, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\resources\\views\\crud/inc/ajax_error_frame.blade.phpcrud::inc.ajax_error_frame", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fresources%2Fviews%2Fcrud%2Finc%2Fajax_error_frame.blade.php&line=1", "ajax": false, "filename": "ajax_error_frame.blade.php", "line": "?"}, "render_count": 1, "name_original": "crud::inc.ajax_error_frame"}, {"name": "1x backpack.theme-tabler::inc.theme_scripts", "param_count": null, "params": [], "start": 1753757866.281918, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\crm.hri.com.vn\\vendor/backpack/theme-tabler/resources/views/inc/theme_scripts.blade.phpbackpack.theme-tabler::inc.theme_scripts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Ftheme-tabler%2Fresources%2Fviews%2Finc%2Ftheme_scripts.blade.php&line=1", "ajax": false, "filename": "theme_scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "backpack.theme-tabler::inc.theme_scripts"}]}, "route": {"uri": "GET admin/apply-job/{id}/show", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin, Closure", "as": "apply-job.show", "operation": "show", "controller": "App\\Http\\Controllers\\Admin\\ApplyJobCrudController@show", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FShowOperation.php&line=71\" onclick=\"\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ShowOperation.php:71-90</a>"}, "queries": {"nb_statements": 24, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06506999999999999, "accumulated_duration_str": "65.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.416365, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.430514, "duration": 0.01572, "duration_str": "15.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 24.159}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.5287151, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "c_hri", "explain": null, "start_percent": 24.159, "width_percent": 1.045}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.531538, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "c_hri", "explain": null, "start_percent": 25.204, "width_percent": 0.953}, {"sql": "select * from `apply_jobs` where `apply_jobs`.`id` = '1890' limit 1", "type": "query", "params": [], "bindings": ["1890"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 50}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 380}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}], "start": **********.542264, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Read.php:76", "source": {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=76", "ajax": false, "filename": "Read.php", "line": "76"}, "connection": "c_hri", "explain": null, "start_percent": 26.156, "width_percent": 1.629}, {"sql": "select * from `jobs` where `jobs`.`id` = 298 and `jobs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 165}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.546847, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApplyJobCrudController.php:381", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ApplyJobCrudController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\ApplyJobCrudController.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FApplyJobCrudController.php&line=381", "ajax": false, "filename": "ApplyJobCrudController.php", "line": "381"}, "connection": "c_hri", "explain": null, "start_percent": 27.785, "width_percent": 0.815}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 73}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.842176, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 28.6, "width_percent": 1.429}, {"sql": "select * from `career_levels` where `career_levels`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 92}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.847407, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:92", "source": {"index": 21, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=92", "ajax": false, "filename": "show.blade.php", "line": "92"}, "connection": "c_hri", "explain": null, "start_percent": 30.029, "width_percent": 0.738}, {"sql": "select `skills`.*, `cv_skills`.`cv_id` as `pivot_cv_id`, `cv_skills`.`skill_id` as `pivot_skill_id` from `skills` inner join `cv_skills` on `skills`.`id` = `cv_skills`.`skill_id` where `cv_skills`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 105}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.84956, "duration": 0.010289999999999999, "duration_str": "10.29ms", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:105", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=105", "ajax": false, "filename": "show.blade.php", "line": "105"}, "connection": "c_hri", "explain": null, "start_percent": 30.767, "width_percent": 15.814}, {"sql": "select `career_languages`.*, `cv_career_languages`.`cv_id` as `pivot_cv_id`, `cv_career_languages`.`career_language_id` as `pivot_career_language_id` from `career_languages` inner join `cv_career_languages` on `career_languages`.`id` = `cv_career_languages`.`career_language_id` where `cv_career_languages`.`cv_id` = 115121", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 115}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.863701, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:115", "source": {"index": 20, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=115", "ajax": false, "filename": "show.blade.php", "line": "115"}, "connection": "c_hri", "explain": null, "start_percent": 46.581, "width_percent": 1.014}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 28, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8656251, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 47.595, "width_percent": 0.999}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:45', '2025-07-29 09:57:45', '2025-07-29 09:57:45')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:45", "2025-07-29 09:57:45", "2025-07-29 09:57:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 30, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.868015, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 48.594, "width_percent": 1.229}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.875271, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 49.823, "width_percent": 0.845}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:45', '2025-07-29 09:57:45', '2025-07-29 09:57:45')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:45", "2025-07-29 09:57:45", "2025-07-29 09:57:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 136}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.876997, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 50.669, "width_percent": 0.692}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 137}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.878423, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 51.36, "width_percent": 0.768}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_public', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:45', '2025-07-29 09:57:45', '2025-07-29 09:57:45')", "type": "query", "params": [], "bindings": [115121, "cv_public", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:45", "2025-07-29 09:57:45", "2025-07-29 09:57:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 137}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.881569, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 52.128, "width_percent": 0.784}, {"sql": "select * from `cvs` where `cvs`.`id` = 115121 and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115121], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, {"index": 27, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 146}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.884169, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "function.php:133", "source": {"index": 20, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fhelpers%2Ffunction.php&line=133", "ajax": false, "filename": "function.php", "line": "133"}, "connection": "c_hri", "explain": null, "start_percent": 52.912, "width_percent": 0.753}, {"sql": "insert into `cv_access_logs` (`cv_id`, `field_name`, `user_id`, `user_name`, `ip_address`, `user_agent`, `accessed_at`, `updated_at`, `created_at`) values (115121, 'cv_private', 1, '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-29 09:57:45', '2025-07-29 09:57:45', '2025-07-29 09:57:45')", "type": "query", "params": [], "bindings": [115121, "cv_private", 1, "<EMAIL>", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "2025-07-29 09:57:45", "2025-07-29 09:57:45", "2025-07-29 09:57:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, {"index": 22, "namespace": null, "name": "helpers/function.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\helpers\\function.php", "line": 157}, {"index": 29, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 146}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.887444, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CvAccessLog.php:60", "source": {"index": 21, "namespace": null, "name": "app/Models/CvAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\CvAccessLog.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCvAccessLog.php&line=60", "ajax": false, "filename": "CvAccessLog.php", "line": "60"}, "connection": "c_hri", "explain": null, "start_percent": 53.665, "width_percent": 0.753}, {"sql": "select * from `meta` where `meta`.`owner_type` = 'App\\\\Models\\\\ApplyJob' and `meta`.`owner_id` = 1890 and `meta`.`owner_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\ApplyJob", 1890], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 118}, {"index": 22, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/GetMeta.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\GetMeta.php", "line": 24}, {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 150}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753757866.0485802, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 54.418, "width_percent": 3.15}, {"sql": "select * from `status` where `status`.`id` = 81 limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 22, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": 1753757866.053631, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 21, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "c_hri", "explain": null, "start_percent": 57.569, "width_percent": 0.738}, {"sql": "select * from `status` where `status`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757866.0555718, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "admins.apply_job.show:226", "source": {"index": 23, "namespace": "view", "name": "admins.apply_job.show", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\resources\\views/admins/apply_job/show.blade.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fresources%2Fviews%2Fadmins%2Fapply_job%2Fshow.blade.php&line=226", "ajax": false, "filename": "show.blade.php", "line": "226"}, "connection": "c_hri", "explain": null, "start_percent": 58.306, "width_percent": 0.645}, {"sql": "select * from `notifications` where `user_id` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": 1753757866.185062, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ClientViewComposer.php:19", "source": {"index": 15, "namespace": null, "name": "app/ViewComposers/ClientViewComposer.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\ViewComposers\\ClientViewComposer.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FViewComposers%2FClientViewComposer.php&line=19", "ajax": false, "filename": "ClientViewComposer.php", "line": "19"}, "connection": "c_hri", "explain": null, "start_percent": 58.952, "width_percent": 1.183}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757866.1958282, "duration": 0.018699999999999998, "duration_str": "18.7ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 60.135, "width_percent": 28.738}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757866.231777, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 88.874, "width_percent": 5.64}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'c_hri' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": 1753757866.2392979, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "helpers.php:134", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fhelpers.php&line=134", "ajax": false, "filename": "helpers.php", "line": "134"}, "connection": "c_hri", "explain": null, "start_percent": 94.514, "width_percent": 5.486}]}, "models": {"data": {"App\\Models\\Notification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FNotification.php&line=1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\Cv": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}, "App\\Models\\Status": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FStatus.php&line=1", "ajax": false, "filename": "Status.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ApplyJob": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\Job": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\CareerLevel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLevel.php&line=1", "ajax": false, "filename": "CareerLevel.php", "line": "?"}}, "App\\Models\\Skill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FSkill.php&line=1", "ajax": false, "filename": "Skill.php", "line": "?"}}, "App\\Models\\CareerLanguage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCareerLanguage.php&line=1", "ajax": false, "filename": "CareerLanguage.php", "line": "?"}}}, "count": 24, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 45, "messages": [{"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2097530008 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097530008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53792, "xdebug_link": null}, {"message": "[\n  ability => apply-job.edit,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1553915858 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553915858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539783, "xdebug_link": null}, {"message": "[\n  ability => apply-job.show,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-65873662 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.show </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">apply-job.show</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65873662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541082, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2058007998 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058007998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.154304, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128775804 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128775804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.156314, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.157263, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821720696 data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821720696\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.158199, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-366505333 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366505333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.160248, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-803815064 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803815064\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.161095, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1977449778 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977449778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.161879, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1910742477 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910742477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.163234, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190976669 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190976669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.164051, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-583335057 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583335057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.164857, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1314418062 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314418062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.165679, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.166522, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255446482 data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255446482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.168516, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1847279770 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847279770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.16995, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1215638780 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215638780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.170704, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-17543913 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17543913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.172012, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119348648 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119348648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.172996, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1628642647 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628642647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.176074, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-747059806 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747059806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.177402, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-146265977 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146265977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757866.179501, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-707309746 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707309746\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757866.181106, "xdebug_link": null}, {"message": "[\n  ability => task.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-103672211 data-indent-pad=\"  \"><span class=sf-dump-note>task.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">task.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103672211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.248218, "xdebug_link": null}, {"message": "[\n  ability => lead.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1916132576 data-indent-pad=\"  \"><span class=sf-dump-note>lead.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lead.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916132576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.249352, "xdebug_link": null}, {"message": "[\n  ability => company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.250312, "xdebug_link": null}, {"message": "[\n  ability => job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.251303, "xdebug_link": null}, {"message": "[\n  ability => apply-job.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-578111678 data-indent-pad=\"  \"><span class=sf-dump-note>apply-job.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">apply-job.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578111678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.25315, "xdebug_link": null}, {"message": "[\n  ability => candidate.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1116136539 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">candidate.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116136539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.253994, "xdebug_link": null}, {"message": "[\n  ability => cv.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466389599 data-indent-pad=\"  \"><span class=sf-dump-note>cv.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cv.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466389599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.254784, "xdebug_link": null}, {"message": "[\n  ability => status.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-956423881 data-indent-pad=\"  \"><span class=sf-dump-note>status.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956423881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.25613, "xdebug_link": null}, {"message": "[\n  ability => academic-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-387294795 data-indent-pad=\"  \"><span class=sf-dump-note>academic-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">academic-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387294795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.256982, "xdebug_link": null}, {"message": "[\n  ability => career-language.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544657348 data-indent-pad=\"  \"><span class=sf-dump-note>career-language.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">career-language.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544657348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.257853, "xdebug_link": null}, {"message": "[\n  ability => career-level.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1718803570 data-indent-pad=\"  \"><span class=sf-dump-note>career-level.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">career-level.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1718803570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.258726, "xdebug_link": null}, {"message": "[\n  ability => skill.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1574145239 data-indent-pad=\"  \"><span class=sf-dump-note>skill.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">skill.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574145239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.259633, "xdebug_link": null}, {"message": "[\n  ability => internal-company.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>internal-company.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">internal-company.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.26165, "xdebug_link": null}, {"message": "[\n  ability => department.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1082671859 data-indent-pad=\"  \"><span class=sf-dump-note>department.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">department.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082671859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.26274, "xdebug_link": null}, {"message": "[\n  ability => user.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1468162620 data-indent-pad=\"  \"><span class=sf-dump-note>user.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468162620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.263429, "xdebug_link": null}, {"message": "[\n  ability => role.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927418643 data-indent-pad=\"  \"><span class=sf-dump-note>role.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">role.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927418643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.264388, "xdebug_link": null}, {"message": "[\n  ability => permission.index,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1691788535 data-indent-pad=\"  \"><span class=sf-dump-note>permission.index </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">permission.index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691788535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.26539, "xdebug_link": null}, {"message": "[\n  ability => report.only-team,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-580299297 data-indent-pad=\"  \"><span class=sf-dump-note>report.only-team </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">report.only-team</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580299297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.267629, "xdebug_link": null}, {"message": "[\n  ability => report.lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1876705402 data-indent-pad=\"  \"><span class=sf-dump-note>report.lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">report.lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876705402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753757866.268819, "xdebug_link": null}, {"message": "[\n  ability => statistical.change-status-lead,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1171981062 data-indent-pad=\"  \"><span class=sf-dump-note>statistical.change-status-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">statistical.change-status-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171981062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757866.270632, "xdebug_link": null}, {"message": "[\n  ability => meeting-room.manager,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1526561091 data-indent-pad=\"  \"><span class=sf-dump-note>meeting-room.manager </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">meeting-room.manager</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526561091\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1753757866.272211, "xdebug_link": null}]}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/apply-job/1890/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/apply-job/1890/show", "status_code": "<pre class=sf-dump id=sf-dump-1346712710 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1346712710\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1422716196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1422716196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1652586105 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1652586105\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-956614492 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/apply-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFFWmtvUDFUODhGQ1daRUpsUWw4RUE9PSIsInZhbHVlIjoiZDlKYmIrN3Z0a2E2L01wMWk0NWdBM3EwVjU3cGY5MlhhNnUraWxPTlozS3BsNmhDNHUzbTY3UDhpUGxrZU9tbGV0QjJLK2d2dk5HekdRcHFGWWFmN3JNbm1lS2NMZTFvZGFRa3VDU2xNSllYUFdYSzVGMGYyNjFmajZNUmVuUVUiLCJtYWMiOiI5MjlkOTAwMDA4Yzg4YzFlNTk1MGNkOTRlN2JjM2FhY2FjMTY5MTQ3YWQ3NDUzM2M4ZjgxMmNlNzkwZTUyNzdiIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImpxQnpoamV4aFpHeXZEYk55cFVINlE9PSIsInZhbHVlIjoiV0pwemk2aG5VYzFmSUl1bVhmNnozbTUvV1o4M1FRWUhxemZGU3RUMFM4a1k2YVpmR3d3cHVMRk1KeWw0MGxWM1ZtaFc1aGdoaE1ycllLbUkzWFAwdnAreVczTFQ5NUVkYSthQnBqemNJbzUyTFVkdFFMMmZnbElnUkVpV0UwcnEiLCJtYWMiOiI3MjhiMDdmNjY2NDRlNWEwZDY1MWI4NTkzNjAzOTZiMzQyYTFlNWE3M2E2ZWM1YTcxY2VjOGNhYTE0YTMyMDA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956614492\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992191552 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992191552\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2001360456 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFCdVE0eE9YRTBsREJpMmJ3SnJUNFE9PSIsInZhbHVlIjoia1ZsNUFoNUE1eWxpRDdkOVQzL2dBa0xoTWc5Y2h3d2N6Y00wN0hTZ2JzQlpnKzJNa1JNcW9nSURlRHpjWVZtWXVuR1lNblhnY0wzR2pYSEVzc09uSVRpL1B3OWQzaTNJVkVpaFNLUVNMWTg2clRpSDhHWTVTY3pYUXNlQU9tdlEiLCJtYWMiOiI2ZTk1NDZmM2E5NWQ3NDZiZGZlYjQzN2MxYjNjMjczZWJmNjYzZTVmYjRhNmY2ZTZkMWVhMDE2NDc4ODhiZGRiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IlcwZVZZV0dpUU44cUJjRUVxZU1wYlE9PSIsInZhbHVlIjoiRFluQXRCTExxUmVlV0xSdmhwRkU3eHZTZWJETkRIeStJcE9HZmZiaUt6UEgwSkF6K2VyM2dFdXFJSkpUWUIrbm54TlZpR0FWWlVvTURjOFYrb3FCaTZJWFFyRUYxY25heS9DODhRdzRTRnNGeHBJb0pYWFpuMXJjbWd2UkZvdksiLCJtYWMiOiJiZGY5ZmE4YjUxMDI5NGQyNjU1ZTYxNzE4ZWFkNjg5ZmIyMmQyMDBiZTk5NmQ5YzNkMmI3MDg4ZDNlYzI2MmM4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFCdVE0eE9YRTBsREJpMmJ3SnJUNFE9PSIsInZhbHVlIjoia1ZsNUFoNUE1eWxpRDdkOVQzL2dBa0xoTWc5Y2h3d2N6Y00wN0hTZ2JzQlpnKzJNa1JNcW9nSURlRHpjWVZtWXVuR1lNblhnY0wzR2pYSEVzc09uSVRpL1B3OWQzaTNJVkVpaFNLUVNMWTg2clRpSDhHWTVTY3pYUXNlQU9tdlEiLCJtYWMiOiI2ZTk1NDZmM2E5NWQ3NDZiZGZlYjQzN2MxYjNjMjczZWJmNjYzZTVmYjRhNmY2ZTZkMWVhMDE2NDc4ODhiZGRiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IlcwZVZZV0dpUU44cUJjRUVxZU1wYlE9PSIsInZhbHVlIjoiRFluQXRCTExxUmVlV0xSdmhwRkU3eHZTZWJETkRIeStJcE9HZmZiaUt6UEgwSkF6K2VyM2dFdXFJSkpUWUIrbm54TlZpR0FWWlVvTURjOFYrb3FCaTZJWFFyRUYxY25heS9DODhRdzRTRnNGeHBJb0pYWFpuMXJjbWd2UkZvdksiLCJtYWMiOiJiZGY5ZmE4YjUxMDI5NGQyNjU1ZTYxNzE4ZWFkNjg5ZmIyMmQyMDBiZTk5NmQ5YzNkMmI3MDg4ZDNlYzI2MmM4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001360456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1595083957 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595083957\", {\"maxDepth\":0})</script>\n"}}