{"__meta": {"id": "Xc5ca3eb3c2cbbf0e7ad726fc576d3bd5", "datetime": "2025-07-29 09:57:37", "utime": **********.512506, "method": "GET", "uri": "/api/created-apply-job-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753757856.913986, "end": **********.512524, "duration": 0.5985379219055176, "duration_str": "599ms", "measures": [{"label": "Booting", "start": 1753757856.913986, "relative_start": 0, "end": **********.215017, "relative_end": **********.215017, "duration": 0.30103111267089844, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.215027, "relative_start": 0.3010411262512207, "end": **********.512526, "relative_end": 2.1457672119140625e-06, "duration": 0.2974989414215088, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30776992, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/created-apply-job-chart", "middleware": "api, web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Api\\Admin\\ApplyJobController@getApplyJobChart", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FApi%2FAdmin%2FApplyJobController.php&line=55\" onclick=\"\">app/Http/Controllers/Api/Admin/ApplyJobController.php:55-58</a>"}, "queries": {"nb_statements": 84, "nb_visible_statements": 85, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12056000000000003, "accumulated_duration_str": "121ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.272873, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.287378, "duration": 0.0258, "duration_str": "25.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 21.4}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-01' and '2024-01-07'", "type": "query", "params": [], "bindings": ["2024-01-01", "2024-01-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.324836, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 21.4, "width_percent": 1.211}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-08' and '2024-01-14'", "type": "query", "params": [], "bindings": ["2024-01-08", "2024-01-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.327948, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 22.611, "width_percent": 1.899}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-15' and '2024-01-21'", "type": "query", "params": [], "bindings": ["2024-01-15", "2024-01-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.331234, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 24.511, "width_percent": 1.841}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-22' and '2024-01-28'", "type": "query", "params": [], "bindings": ["2024-01-22", "2024-01-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.334464, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 26.352, "width_percent": 1.858}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-01-29' and '2024-02-04'", "type": "query", "params": [], "bindings": ["2024-01-29", "2024-02-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.337719, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 28.21, "width_percent": 1.883}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-05' and '2024-02-11'", "type": "query", "params": [], "bindings": ["2024-02-05", "2024-02-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.340985, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 30.093, "width_percent": 1.875}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-12' and '2024-02-18'", "type": "query", "params": [], "bindings": ["2024-02-12", "2024-02-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.344245, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 31.967, "width_percent": 1.85}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-19' and '2024-02-25'", "type": "query", "params": [], "bindings": ["2024-02-19", "2024-02-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3474731, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 33.817, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-02-26' and '2024-03-03'", "type": "query", "params": [], "bindings": ["2024-02-26", "2024-03-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3506851, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 35.642, "width_percent": 1.858}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-04' and '2024-03-10'", "type": "query", "params": [], "bindings": ["2024-03-04", "2024-03-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.35394, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 37.5, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-11' and '2024-03-17'", "type": "query", "params": [], "bindings": ["2024-03-11", "2024-03-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.356841, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 39.084, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-18' and '2024-03-24'", "type": "query", "params": [], "bindings": ["2024-03-18", "2024-03-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.358851, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 39.914, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-03-25' and '2024-03-31'", "type": "query", "params": [], "bindings": ["2024-03-25", "2024-03-31"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.360858, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 40.751, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-01' and '2024-04-07'", "type": "query", "params": [], "bindings": ["2024-04-01", "2024-04-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.362867, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 41.581, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-08' and '2024-04-14'", "type": "query", "params": [], "bindings": ["2024-04-08", "2024-04-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.364865, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 42.41, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-15' and '2024-04-21'", "type": "query", "params": [], "bindings": ["2024-04-15", "2024-04-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.366868, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 43.24, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-22' and '2024-04-28'", "type": "query", "params": [], "bindings": ["2024-04-22", "2024-04-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.368858, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 44.061, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-04-29' and '2024-05-05'", "type": "query", "params": [], "bindings": ["2024-04-29", "2024-05-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.37086, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 44.899, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-06' and '2024-05-12'", "type": "query", "params": [], "bindings": ["2024-05-06", "2024-05-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.372868, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 45.728, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-13' and '2024-05-19'", "type": "query", "params": [], "bindings": ["2024-05-13", "2024-05-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3748732, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 46.558, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-20' and '2024-05-26'", "type": "query", "params": [], "bindings": ["2024-05-20", "2024-05-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.376879, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 47.395, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-05-27' and '2024-06-02'", "type": "query", "params": [], "bindings": ["2024-05-27", "2024-06-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.37887, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 48.225, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-03' and '2024-06-09'", "type": "query", "params": [], "bindings": ["2024-06-03", "2024-06-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.380845, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 49.046, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-10' and '2024-06-16'", "type": "query", "params": [], "bindings": ["2024-06-10", "2024-06-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.382808, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 49.859, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-17' and '2024-06-23'", "type": "query", "params": [], "bindings": ["2024-06-17", "2024-06-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.384823, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 50.688, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-06-24' and '2024-06-30'", "type": "query", "params": [], "bindings": ["2024-06-24", "2024-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.386836, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 51.535, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-01' and '2024-07-07'", "type": "query", "params": [], "bindings": ["2024-07-01", "2024-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.388811, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 52.356, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-08' and '2024-07-14'", "type": "query", "params": [], "bindings": ["2024-07-08", "2024-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.390785, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 53.169, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-15' and '2024-07-21'", "type": "query", "params": [], "bindings": ["2024-07-15", "2024-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.392787, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 53.998, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-22' and '2024-07-28'", "type": "query", "params": [], "bindings": ["2024-07-22", "2024-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3947752, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 54.827, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-07-29' and '2024-08-04'", "type": "query", "params": [], "bindings": ["2024-07-29", "2024-08-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3967562, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 55.649, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-05' and '2024-08-11'", "type": "query", "params": [], "bindings": ["2024-08-05", "2024-08-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3987272, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 56.47, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-12' and '2024-08-18'", "type": "query", "params": [], "bindings": ["2024-08-12", "2024-08-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.400741, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 57.299, "width_percent": 0.879}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-19' and '2024-08-25'", "type": "query", "params": [], "bindings": ["2024-08-19", "2024-08-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.40279, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 58.179, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-08-26' and '2024-09-01'", "type": "query", "params": [], "bindings": ["2024-08-26", "2024-09-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.404786, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 59, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-02' and '2024-09-08'", "type": "query", "params": [], "bindings": ["2024-09-02", "2024-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.40679, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 59.821, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-09' and '2024-09-15'", "type": "query", "params": [], "bindings": ["2024-09-09", "2024-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4088042, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 60.659, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-16' and '2024-09-22'", "type": "query", "params": [], "bindings": ["2024-09-16", "2024-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.410803, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 61.48, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-23' and '2024-09-29'", "type": "query", "params": [], "bindings": ["2024-09-23", "2024-09-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.412807, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 62.309, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-09-30' and '2024-10-06'", "type": "query", "params": [], "bindings": ["2024-09-30", "2024-10-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.414784, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 63.13, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-07' and '2024-10-13'", "type": "query", "params": [], "bindings": ["2024-10-07", "2024-10-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.416811, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 63.976, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-14' and '2024-10-20'", "type": "query", "params": [], "bindings": ["2024-10-14", "2024-10-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.418792, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 64.789, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-21' and '2024-10-27'", "type": "query", "params": [], "bindings": ["2024-10-21", "2024-10-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.420788, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 65.61, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-10-28' and '2024-11-03'", "type": "query", "params": [], "bindings": ["2024-10-28", "2024-11-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.422811, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 66.448, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-04' and '2024-11-10'", "type": "query", "params": [], "bindings": ["2024-11-04", "2024-11-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.424808, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 67.278, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-11' and '2024-11-17'", "type": "query", "params": [], "bindings": ["2024-11-11", "2024-11-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.426803, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 68.107, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-18' and '2024-11-24'", "type": "query", "params": [], "bindings": ["2024-11-18", "2024-11-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4288142, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 68.937, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-11-25' and '2024-12-01'", "type": "query", "params": [], "bindings": ["2024-11-25", "2024-12-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4308162, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 69.774, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-02' and '2024-12-08'", "type": "query", "params": [], "bindings": ["2024-12-02", "2024-12-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.432812, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 70.604, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-09' and '2024-12-15'", "type": "query", "params": [], "bindings": ["2024-12-09", "2024-12-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.434812, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 71.433, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-16' and '2024-12-22'", "type": "query", "params": [], "bindings": ["2024-12-16", "2024-12-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.436819, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 72.263, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-23' and '2024-12-29'", "type": "query", "params": [], "bindings": ["2024-12-23", "2024-12-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4388242, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 73.101, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2024-12-30' and '2025-01-05'", "type": "query", "params": [], "bindings": ["2024-12-30", "2025-01-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.44081, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 73.93, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-06' and '2025-01-12'", "type": "query", "params": [], "bindings": ["2025-01-06", "2025-01-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4428082, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 74.759, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-13' and '2025-01-19'", "type": "query", "params": [], "bindings": ["2025-01-13", "2025-01-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.444798, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 75.589, "width_percent": 0.854}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-20' and '2025-01-26'", "type": "query", "params": [], "bindings": ["2025-01-20", "2025-01-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.446815, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 76.443, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-01-27' and '2025-02-02'", "type": "query", "params": [], "bindings": ["2025-01-27", "2025-02-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.448823, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 77.289, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-03' and '2025-02-09'", "type": "query", "params": [], "bindings": ["2025-02-03", "2025-02-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.450829, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 78.119, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-10' and '2025-02-16'", "type": "query", "params": [], "bindings": ["2025-02-10", "2025-02-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4528391, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 78.957, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-17' and '2025-02-23'", "type": "query", "params": [], "bindings": ["2025-02-17", "2025-02-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4548612, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 79.794, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-02-24' and '2025-03-02'", "type": "query", "params": [], "bindings": ["2025-02-24", "2025-03-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.456886, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 80.64, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-03' and '2025-03-09'", "type": "query", "params": [], "bindings": ["2025-03-03", "2025-03-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.458888, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 81.47, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-10' and '2025-03-16'", "type": "query", "params": [], "bindings": ["2025-03-10", "2025-03-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4608722, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 82.299, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-17' and '2025-03-23'", "type": "query", "params": [], "bindings": ["2025-03-17", "2025-03-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.462846, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 83.12, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-24' and '2025-03-30'", "type": "query", "params": [], "bindings": ["2025-03-24", "2025-03-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.464839, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 83.95, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-03-31' and '2025-04-06'", "type": "query", "params": [], "bindings": ["2025-03-31", "2025-04-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.466841, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 84.779, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-07' and '2025-04-13'", "type": "query", "params": [], "bindings": ["2025-04-07", "2025-04-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.468864, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 85.625, "width_percent": 0.888}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-14' and '2025-04-20'", "type": "query", "params": [], "bindings": ["2025-04-14", "2025-04-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4709501, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 86.513, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-21' and '2025-04-27'", "type": "query", "params": [], "bindings": ["2025-04-21", "2025-04-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.472971, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 87.351, "width_percent": 0.854}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-04-28' and '2025-05-04'", "type": "query", "params": [], "bindings": ["2025-04-28", "2025-05-04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.475102, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 88.205, "width_percent": 0.888}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-05' and '2025-05-11'", "type": "query", "params": [], "bindings": ["2025-05-05", "2025-05-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.478575, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 89.093, "width_percent": 0.904}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-12' and '2025-05-18'", "type": "query", "params": [], "bindings": ["2025-05-12", "2025-05-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.481895, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 89.997, "width_percent": 0.912}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-19' and '2025-05-25'", "type": "query", "params": [], "bindings": ["2025-05-19", "2025-05-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4840221, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 90.909, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-05-26' and '2025-06-01'", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.486058, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 91.747, "width_percent": 0.921}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-02' and '2025-06-08'", "type": "query", "params": [], "bindings": ["2025-06-02", "2025-06-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4881701, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 92.668, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-09' and '2025-06-15'", "type": "query", "params": [], "bindings": ["2025-06-09", "2025-06-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4901621, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 93.48, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-16' and '2025-06-22'", "type": "query", "params": [], "bindings": ["2025-06-16", "2025-06-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.492131, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 94.293, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-23' and '2025-06-29'", "type": "query", "params": [], "bindings": ["2025-06-23", "2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.494121, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 95.114, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-06-30' and '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-06-30", "2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.496088, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 95.927, "width_percent": 0.805}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-07' and '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-07", "2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.498039, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 96.732, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-14' and '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-14", "2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5000062, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 97.545, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-21' and '2025-07-27'", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5019982, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 98.366, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `apply_jobs` where `created_at` between '2025-07-28' and '2025-08-03'", "type": "query", "params": [], "bindings": ["2025-07-28", "2025-08-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/Admin/ApplyJobController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Api\\Admin\\ApplyJobController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5039842, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:39", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=39", "ajax": false, "filename": "ApplyJobRepository.php", "line": "39"}, "connection": "c_hri", "explain": null, "start_percent": 99.195, "width_percent": 0.805}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/api/created-apply-job-chart\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/api/created-apply-job-chart", "status_code": "<pre class=sf-dump id=sf-dump-1539480520 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1539480520\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1352464411 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1352464411\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1558491919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1558491919\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-254916969 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJwUGtMUXhLWW1abGVDM2xvSjc5RHc9PSIsInZhbHVlIjoiZHppKzRyN0NTTjBGSEVodXVjUlU3RGRLR0lZaHE2cUtsQWk0U3o0c0NnREFFNFBvU3pPL0tzeEw2Ri81REtIT0o3a01WeG5FaWhZOUlKT2VzbU1LMXhhT3dvYm1TNVNxdVliZEhFbk9iNWN1ekFzMlBURjUxY01hdzVqcUZmOU0iLCJtYWMiOiI4MzMwODY1MmJiYjI3ZDY2MDk5NjAwYzE3ODVlNTJhMWFiODcwYjcwYzNjODUwNDU2MzY3MzkxOGQyYWVhMDZhIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImhiK3ZmTVpMRTcxeCtXSWNhUGxnT3c9PSIsInZhbHVlIjoiV1VLSHpnN242T0FDVnpjRUdDSVpNUTRONmtiMEo3eWhGTTYzOGFkVmZxZFRsdUYzbzFXWGdoQ2NYTVhQQ0J5ZnRNRVN3cU5Xc0V1bzRSeGtud1c5a0pQR1N6bGtqb1hNbU1ITG00UzYwbVBSdUFKVWhkRGVvaUJiY3pmTUZVRGwiLCJtYWMiOiJkZWFjNWI2OGNmMDY1YTQyYmM2ZGRmNDFiZGRlZDA1OGRlZTI5YjYyNDVhMTJlMjQ3OTIzOTI0Y2JiMGIwNTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254916969\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-647281652 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647281652\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1792191867 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im95b3lVSHdwWVFwZ2JtZkpheC9zV0E9PSIsInZhbHVlIjoiKzg0d3MrcVNxKzFhUG92QmhYUGh0UEJESzZEMjJrcjIrU2VxL1N6dm8wQ2VMTFVZakVTQWJXcnVMdFR3Ty9PazdmNFZkdHZDK3p6US85VHRrSnJBKzlSaVdGaTBpMFlMVDlEb1BaUWo2bDFwRjljNkNDc3dQRVk4UmpMaCtiNm4iLCJtYWMiOiI2YmMwYzQwODFmOWIxOTc1MTllMTI4NzIwMDQzY2QyM2Q5NGFjMGE3ZjA5ZDhjMjJmZWM2MmQ0ZGYxNjg1OTU2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6ImhaZ1pnSTAxcDNLa1JwYjBLZm1RSGc9PSIsInZhbHVlIjoiYk1iam8vd3NFTXBJeDBkU0FhT1dGZ0xxcmVUbkUraUJrSHp0eEdpblp0ZnlLR2VocU56bVZVMERMdkVweWZReFpGQzZ6YUNyMVcvbXg4Q2JkSWFtbSt5RzRzbm9tYzlsWWRLT0pEYmM5K0NjbHdiM2FBL1dlbWx5aUdOVkgwcmoiLCJtYWMiOiI5NzNjMmE5ZGU0ZjhhMjkxODk4NzY4MThjZjhhOTA5ZGU0NTk4N2MxOGVhYjg5ZTU2ZWJhMDI2OGQ5NmQxZTM3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im95b3lVSHdwWVFwZ2JtZkpheC9zV0E9PSIsInZhbHVlIjoiKzg0d3MrcVNxKzFhUG92QmhYUGh0UEJESzZEMjJrcjIrU2VxL1N6dm8wQ2VMTFVZakVTQWJXcnVMdFR3Ty9PazdmNFZkdHZDK3p6US85VHRrSnJBKzlSaVdGaTBpMFlMVDlEb1BaUWo2bDFwRjljNkNDc3dQRVk4UmpMaCtiNm4iLCJtYWMiOiI2YmMwYzQwODFmOWIxOTc1MTllMTI4NzIwMDQzY2QyM2Q5NGFjMGE3ZjA5ZDhjMjJmZWM2MmQ0ZGYxNjg1OTU2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6ImhaZ1pnSTAxcDNLa1JwYjBLZm1RSGc9PSIsInZhbHVlIjoiYk1iam8vd3NFTXBJeDBkU0FhT1dGZ0xxcmVUbkUraUJrSHp0eEdpblp0ZnlLR2VocU56bVZVMERMdkVweWZReFpGQzZ6YUNyMVcvbXg4Q2JkSWFtbSt5RzRzbm9tYzlsWWRLT0pEYmM5K0NjbHdiM2FBL1dlbWx5aUdOVkgwcmoiLCJtYWMiOiI5NzNjMmE5ZGU0ZjhhMjkxODk4NzY4MThjZjhhOTA5ZGU0NTk4N2MxOGVhYjg5ZTU2ZWJhMDI2OGQ5NmQxZTM3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792191867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1402355982 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://chri.local/api/created-apply-job-chart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402355982\", {\"maxDepth\":0})</script>\n"}}