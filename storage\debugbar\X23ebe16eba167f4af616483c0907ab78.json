{"__meta": {"id": "X23ebe16eba167f4af616483c0907ab78", "datetime": "2025-07-29 10:49:41", "utime": **********.10328, "method": "GET", "uri": "/admin/secure-file/download?id=115121&model=Cv&field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753760980.654789, "end": **********.103294, "duration": 0.4485049247741699, "duration_str": "449ms", "measures": [{"label": "Booting", "start": 1753760980.654789, "relative_start": 0, "end": 1753760980.965561, "relative_end": 1753760980.965561, "duration": 0.3107719421386719, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753760980.965572, "relative_start": 0.31078314781188965, "end": **********.103298, "relative_end": 4.0531158447265625e-06, "duration": 0.137725830078125, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31975520, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/secure-file/download", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\FileDownloadController@download", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "where": [], "as": "secure-file.download", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=25\" onclick=\"\">app/Http/Controllers/FileDownloadController.php:25-151</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029359999999999997, "accumulated_duration_str": "29.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.013528, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.025646, "duration": 0.02044, "duration_str": "20.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 69.619}, {"sql": "select count(*) as aggregate from `file_access_logs` where `ip_address` = '127.0.0.1' and `accessed_at` >= '2025-07-29 10:39:41' and `is_successful` = 1", "type": "query", "params": [], "bindings": ["127.0.0.1", "2025-07-29 10:39:41", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Services/FileSecurityService.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Services\\FileSecurityService.php", "line": 21}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.077169, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:71", "source": {"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=71", "ajax": false, "filename": "FileAccessLog.php", "line": "71"}, "connection": "c_hri", "explain": null, "start_percent": 69.619, "width_percent": 1.26}, {"sql": "select * from `cvs` where `cvs`.`id` = '115121' and `cvs`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["115121"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 102}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0811799, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "FileDownloadController.php:102", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 102}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=102", "ajax": false, "filename": "FileDownloadController.php", "line": "102"}, "connection": "c_hri", "explain": null, "start_percent": 70.879, "width_percent": 1.26}, {"sql": "insert into `file_access_logs` (`user_id`, `file_path`, `model_type`, `record_id`, `field_name`, `ip_address`, `user_agent`, `hash_token`, `is_successful`, `error_message`, `accessed_at`, `updated_at`, `created_at`) values (null, 'UNKNOWN', 'Cv', '115121', 'cv_private', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c', 0, 'Không có quyền truy cập file này', '2025-07-29 10:49:41', '2025-07-29 10:49:41', '2025-07-29 10:49:41')", "type": "query", "params": [], "bindings": [null, "UNKNOWN", "Cv", "115121", "cv_private", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c", 0, "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập file này", "2025-07-29 10:49:41", "2025-07-29 10:49:41", "2025-07-29 10:49:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 137}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.084177, "duration": 0.00818, "duration_str": "8.18ms", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:81", "source": {"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=81", "ajax": false, "filename": "FileAccessLog.php", "line": "81"}, "connection": "c_hri", "explain": null, "start_percent": 72.139, "width_percent": 27.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Cv": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FCv.php&line=1", "ajax": false, "filename": "Cv.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/secure-file/download?field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c&id=115121&model=Cv\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/secure-file/download", "status_code": "<pre class=sf-dump id=sf-dump-540517787 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-540517787\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-818449884 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115121</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Cv</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cv_private</span>\"\n  \"<span class=sf-dump-key>hash</span>\" => \"<span class=sf-dump-str title=\"64 characters\">4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818449884\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-109313029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-109313029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1065020176 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://chri.local/admin/apply-job/1890/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldKT0hkR29LRWhHU3U0QXhYTzFqdnc9PSIsInZhbHVlIjoiN3VjRGljbTNlbDhmSitvMjVHTG9VY3NYcXBUQzRUcDFOVmNzTlNqYmw2RHVjb0FPazdlSjhFNHFDQ2ZDa2V1UXYzT3g1ZndoRUkyaUtSdCszSU9TbnRWb1JkTkZnRjRVMlBwKzZXVFRwQ3lLS1FBd2Z4dEUzYXNsSkdHSUZ6RmgiLCJtYWMiOiIwYzkzYTU2YjFlMGVjNTA0NjI0MGQ1ZjZiYjg1Y2JlM2NjNWIxMzZkNjQ0MTUyOWE0NmRlOTJjZmI5YzM4ZGZlIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IjZ0T1htN0J2ZWxvZHBWR0NBTnhnVkE9PSIsInZhbHVlIjoiU2g1K1hHK3hzQ1JKRVA5RWN5MWxTT04xdDh1azNRT295M0k3aDRkenZqblBKaUtWTzNpTVhPRit0TElKenozc2xlYnRyeTVWeXh0ME5uNzVIU09aUG9NYkY2ckZCUW9GN1NqMjZHS1hZbGs4QnZVWm1WYTlEVDVKVTV2dWdQZlgiLCJtYWMiOiIwODEzYzM0ZDZhYWRhNGE1ZjM0YmY1Yzg2M2JlYjI4YmFjNmQ0ZTBlZjM0MGQ3MGQ3YjY0Y2U4YjEyOTdjY2Y0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065020176\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2121982797 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121982797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1418460743 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:49:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlljcUZPK2NsZ24zZ0xvdTB3cWVEaGc9PSIsInZhbHVlIjoiMi94b0cvMlc1NXBSZU45MzBWRVJSMWwyV1dyaDZaKzVTVlVQajd3SXd6d0JjWVJRRE4ydFZ2UjN3bFJpV2hCSnNVeis3Sm15NDBKL1ZOTXFielhWazloQ09sQlp2K2VTZkoxaXhSVkdUWjR4Z1YrV0dpZjJTc3Y3ZXQvV0VzeWkiLCJtYWMiOiJkMmUxYjM2NWVjMzM1NWE0NDU3YTJlN2U5MzBhZTg3MDI0OWVjZTk2MzkzNGE4MmMyNTNiZjkxMWY1NTVhNzhjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6InFpLzlXQVg2U3lnaFozSTg3cSsyV0E9PSIsInZhbHVlIjoicUR5R28vc2EvSXRUSEduQ3FVT1NqNkZyWG9sV1RLZ1h0cDhBa0VocnNnZEtoVFFVVWFGcXdOaEZpWW01RUZCSnBubEpWeERhUGNrOW5abVdocmEraitBNk90b3RrdjJ3RElNckJ6VkpoNWtra1VrMUdjVUtMaC8rRmJaU2o1LzkiLCJtYWMiOiI3NDE3NzQ0N2E4ZmE4YzdiNzg1YTEyMWRjYmU0MWYxZjA5Y2M2OGI2NjEyMTg2NGVkODM2Mjg5YWUxMTllNTJiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlljcUZPK2NsZ24zZ0xvdTB3cWVEaGc9PSIsInZhbHVlIjoiMi94b0cvMlc1NXBSZU45MzBWRVJSMWwyV1dyaDZaKzVTVlVQajd3SXd6d0JjWVJRRE4ydFZ2UjN3bFJpV2hCSnNVeis3Sm15NDBKL1ZOTXFielhWazloQ09sQlp2K2VTZkoxaXhSVkdUWjR4Z1YrV0dpZjJTc3Y3ZXQvV0VzeWkiLCJtYWMiOiJkMmUxYjM2NWVjMzM1NWE0NDU3YTJlN2U5MzBhZTg3MDI0OWVjZTk2MzkzNGE4MmMyNTNiZjkxMWY1NTVhNzhjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6InFpLzlXQVg2U3lnaFozSTg3cSsyV0E9PSIsInZhbHVlIjoicUR5R28vc2EvSXRUSEduQ3FVT1NqNkZyWG9sV1RLZ1h0cDhBa0VocnNnZEtoVFFVVWFGcXdOaEZpWW01RUZCSnBubEpWeERhUGNrOW5abVdocmEraitBNk90b3RrdjJ3RElNckJ6VkpoNWtra1VrMUdjVUtMaC8rRmJaU2o1LzkiLCJtYWMiOiI3NDE3NzQ0N2E4ZmE4YzdiNzg1YTEyMWRjYmU0MWYxZjA5Y2M2OGI2NjEyMTg2NGVkODM2Mjg5YWUxMTllNTJiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418460743\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-152336340 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"150 characters\">http://chri.local/admin/secure-file/download?field=cv_private&amp;hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad6c&amp;id=115121&amp;model=Cv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152336340\", {\"maxDepth\":0})</script>\n"}}