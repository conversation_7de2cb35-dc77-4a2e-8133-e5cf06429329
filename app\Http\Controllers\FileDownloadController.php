<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FileAccessLog;
use App\Models\Cv;
use App\Models\ApplyJob;
use App\Models\Job;
use App\Models\Candidate;
use App\Models\CandidateAttachment;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Services\FileSecurityService;

class FileDownloadController extends Controller
{
    /**
     * Download file an toàn với logging và rate limiting
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function download(Request $request)
    {
        try {
            // Validate input parameters
            $validated = $request->validate([
                'id' => 'required|integer',
                'model' => 'required|string|max:100',
                'field' => 'required|string|max:100',
                'hash' => 'required|string|size:64'
            ]);

            $id = $validated['id'];
            $modelName = $validated['model'];
            $fieldName = $validated['field'];
            $hash = $validated['hash'];

            // Kiểm tra IP có bị block không
            $ip = $request->ip();
            if (FileSecurityService::isIpBlocked($ip)) {
                return response()->json([
                    'error' => 'IP của bạn đã bị tạm thời chặn do hoạt động đáng ngờ.'
                ], 403);
            }

            // Kiểm tra rate limiting
            if (!FileSecurityService::checkRateLimit($ip)) {
                // Detect suspicious patterns
                $suspiciousPatterns = FileSecurityService::detectSuspiciousActivity($ip, auth()->id());
                
                // Block IP if suspicious activity detected
                if (!empty($suspiciousPatterns)) {
                    FileSecurityService::blockIpTemporarily($ip, 120); // Block for 2 hours
                }
                
                FileSecurityService::sendRateLimitAlert($ip, auth()->id(), [
                    'suspicious_patterns' => $suspiciousPatterns,
                    'user_agent' => $request->userAgent()
                ]);
                
                FileAccessLog::createLog([
                    'file_path' => 'RATE_LIMITED',
                    'model_type' => $modelName,
                    'record_id' => $id,
                    'field_name' => $fieldName,
                    'hash_token' => $hash,
                    'is_successful' => false,
                    'error_message' => 'Rate limit exceeded: ' . $ip
                ]);

                return response()->json([
                    'error' => 'Quá nhiều yêu cầu. Vui lòng thử lại sau.'
                ], 429);
            }

            // Verify hash token
            if (!FileSecurityService::verifyHash($id, $modelName, $fieldName, $hash)) {
                FileAccessLog::createLog([
                    'file_path' => 'INVALID_HASH',
                    'model_type' => $modelName,
                    'record_id' => $id,
                    'field_name' => $fieldName,
                    'hash_token' => $hash,
                    'is_successful' => false,
                    'error_message' => 'Invalid hash token'
                ]);

                return response()->json([
                    'error' => 'Token không hợp lệ.'
                ], 403);
            }

            // Get model instance
            $modelClass = $this->getModelClass($modelName);
            if (!$modelClass) {
                throw new \Exception("Model không được hỗ trợ: {$modelName}");
            }

            $record = $modelClass::find($id);
            if (!$record) {
                throw new \Exception("Không tìm thấy bản ghi với ID: {$id}");
            }

            // Check if field exists and has value
            if (!isset($record->{$fieldName}) || empty($record->{$fieldName})) {
                throw new \Exception("Field {$fieldName} không tồn tại hoặc rỗng");
            }

            $filePath = $record->{$fieldName};

            // Check user permissions (nếu cần)
            // if (!$this->checkUserPermission($record, $modelName)) {
            //     throw new \Exception("Không có quyền truy cập file này");
            // }

            // Generate signed URL from S3
            $signedUrl = $this->generateSignedUrl($filePath);

            // Log successful access
            FileAccessLog::createLog([
                'file_path' => $filePath,
                'model_type' => $modelName,
                'record_id' => $id,
                'field_name' => $fieldName,
                'hash_token' => $hash,
                'is_successful' => true
            ]);

            // Redirect to signed URL
            return redirect($signedUrl);

        } catch (\Exception $e) {
            // Log error
            FileAccessLog::createLog([
                'file_path' => $request->get('file_path', 'UNKNOWN'),
                'model_type' => $request->get('model', 'UNKNOWN'),
                'record_id' => $request->get('id', 0),
                'field_name' => $request->get('field', 'UNKNOWN'),
                'hash_token' => $request->get('hash', 'INVALID'),
                'is_successful' => false,
                'error_message' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate hash token for security
     */
    private function generateHash($id, $model, $field)
    {
        return FileSecurityService::generateHash($id, $model, $field);
    }

    /**
     * Get model class from string
     */
    private function getModelClass($modelName)
    {
        $allowedModels = [
            'Cv' => \App\Models\Cv::class,
            'ApplyJob' => \App\Models\ApplyJob::class,
            'Job' => \App\Models\Job::class,
            'Candidate' => \App\Models\Candidate::class,
            'CandidateAttachment' => \App\Models\CandidateAttachment::class,
        ];

        return $allowedModels[$modelName] ?? null;
    }

    /**
     * Check user permission to access file
     */
    private function checkUserPermission($record, $modelName)
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // Implement permission logic based on model type
        switch ($modelName) {
            case 'Cv':
            case 'ApplyJob':
                // Check if user can access job-related files
                if (method_exists($user, 'canShowDetailJob')) {
                    $jobId = $modelName === 'Cv' ? 
                        $record->applyJobs()->first()?->job_id : 
                        $record->job_id;
                    return $user->canShowDetailJob($jobId);
                }
                break;
            case 'Job':
                // Check job access permission
                if (method_exists($user, 'canShowDetailJob')) {
                    return $user->canShowDetailJob($record->id);
                }
                break;
            case 'Candidate':
            case 'CandidateAttachment':
                // Check candidate access permission
                return true; // Adjust based on your permission system
        }

        return true; // Default allow for now
    }

    /**
     * Generate signed URL for S3 file
     */
    private function generateSignedUrl($filePath)
    {
        // Check if already a full URL
        if (str_starts_with($filePath, 'http')) {
            return $filePath;
        }

        // Check if file exists locally
        if (file_exists(public_path($filePath))) {
            return asset($filePath);
        }

        // Generate S3 signed URL with 1 hour expiration
        return \Storage::disk('s3')->temporaryUrl($filePath, now()->addHour());
    }

}
