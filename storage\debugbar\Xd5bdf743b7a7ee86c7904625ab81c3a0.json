{"__meta": {"id": "Xd5bdf743b7a7ee86c7904625ab81c3a0", "datetime": "2025-07-29 09:57:36", "utime": **********.814762, "method": "GET", "uri": "/api/top-applyjob-dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.195938, "end": **********.814781, "duration": 0.6188428401947021, "duration_str": "619ms", "measures": [{"label": "Booting", "start": **********.195938, "relative_start": 0, "end": **********.525193, "relative_end": **********.525193, "duration": 0.3292548656463623, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.525218, "relative_start": 0.32927989959716797, "end": **********.814783, "relative_end": 2.1457672119140625e-06, "duration": 0.2895650863647461, "duration_str": "290ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30736056, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/top-applyjob-dashboard", "middleware": "api, web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\Admin\\HomeController@index", "namespace": null, "prefix": "api", "where": [], "as": "home.index", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FAdmin%2FHomeController.php&line=20\" onclick=\"\">app/Http/Controllers/Admin/HomeController.php:20-23</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03065, "accumulated_duration_str": "30.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.594099, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.608535, "duration": 0.02691, "duration_str": "26.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 87.798}, {"sql": "select `c`.`name`, `j`.`title` as `job_title`, `company`.`name` as `company_name`, `apply_jobs`.`status`, `status`.`name` as `status_name`, `apply_jobs`.`updated_at`, `users`.`name` as `users_updated_by`, `apply_jobs`.`note` from `apply_jobs` inner join `cvs` as `c` on `apply_jobs`.`cv_id` = `c`.`id` inner join `jobs` as `j` on `apply_jobs`.`job_id` = `j`.`id` inner join `companies` as `company` on `j`.`company_id` = `company`.`id` inner join `status` on `apply_jobs`.`status` = `status`.`id` inner join `users` on `apply_jobs`.`updated_by` = `users`.`id` order by `apply_jobs`.`updated_at` desc limit 20", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/HomeController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\Admin\\HomeController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7289999, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "ApplyJobRepository.php:24", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ApplyJobRepository.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Repositories\\ApplyJobRepository.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FRepositories%2FApplyJobRepository.php&line=24", "ajax": false, "filename": "ApplyJobRepository.php", "line": "24"}, "connection": "c_hri", "explain": null, "start_percent": 87.798, "width_percent": 12.202}]}, "models": {"data": {"App\\Models\\ApplyJob": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FApplyJob.php&line=1", "ajax": false, "filename": "ApplyJob.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 21, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/api/top-applyjob-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1627363901 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1627363901\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-860367896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-860367896\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-614354034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614354034\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-563320028 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijd2NUxHeHY2OHRpV29qVXZIekZ0K2c9PSIsInZhbHVlIjoiU1IvemFRajlWZkxHc0dLSmtZWk96bFhjNVM5WGJEckp0bjE5MkN3WVJhTGdsQWJZUFUxTkpra1QyVWRWcTlGLzF1amlVdTNBYjVkL3BkZElvUDZTKzl6TDZ5dmVOYW1BdnJwNW1iaFdhL2Jld3NaYTQ5SnBmbnFvY0IvK1krT2EiLCJtYWMiOiI3MGI3YjRiYzRjNTJjZTRhOTUzZDcyMjk4MjJhMzVmNjRhMjZkNGU3ZTI3MjcxYTNlMDM1NGM1MTU4YTg5YzhkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijd2NUxHeHY2OHRpV29qVXZIekZ0K2c9PSIsInZhbHVlIjoiU1IvemFRajlWZkxHc0dLSmtZWk96bFhjNVM5WGJEckp0bjE5MkN3WVJhTGdsQWJZUFUxTkpra1QyVWRWcTlGLzF1amlVdTNBYjVkL3BkZElvUDZTKzl6TDZ5dmVOYW1BdnJwNW1iaFdhL2Jld3NaYTQ5SnBmbnFvY0IvK1krT2EiLCJtYWMiOiI3MGI3YjRiYzRjNTJjZTRhOTUzZDcyMjk4MjJhMzVmNjRhMjZkNGU3ZTI3MjcxYTNlMDM1NGM1MTU4YTg5YzhkIiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6ImhQbUFVTWdwWWtmWWtTbDJvbEMxaXc9PSIsInZhbHVlIjoiMzJIb2ZmOEE1QmJhbG85dWxjaVZrQVRQc01HWEJEdDNQMWt5bVlWUk1pMXkzOHloa0RrN0w2d1UydWloYkM1cEhVWW1kY0ZZdXdvTEJvbDJLcWZuQ096UzRqeWx4VnRQNXJpTndvUlJ2Z3FnSHZEcnR4VmJKWmhuV1BqNkFyRjgiLCJtYWMiOiIwZDljMDU0YmEyOTk5ZjJiYTk4MmExZTRmNTQyNTEyZWVkNzVkNDU1ZjZhOWIwNzNiYTcwYWExNWZlNGQ1MDg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563320028\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2067917727 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067917727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1848978189 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 02:57:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImdWSThPc0F2ZmExc2hQYk14Umh0clE9PSIsInZhbHVlIjoiTkYyU3poWmRNb1dDR0pMUURoWDd3ZVdSUVhadFhlYzdGQnMzNFhRNzU0bldTYjVDZm1NOStBc3J0SFJXdTRmVk9QRTZYR2t4V0tWdE1veVFuZHdrQlUyT29FaWo1dXRwK2Y4RUdwUnhUdWVnclJDVlRTbG1maGN6WklvdG9lUXciLCJtYWMiOiIxNjRjNDBlY2Y3YTg0MGQ1MTI2YmM2YzIwYTMwNTQ1YmQ5ODMyYTVjYWY2Y2FkNWEwODFmMGNjZTRkOWZmMmU3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6Ikc1TGI0UGh2RVgwRC9LWGFxd3pGM0E9PSIsInZhbHVlIjoiNlk3YXg0ZHZhYVYrbkN5eUg2blQwS0RYSDc2U0U3ekZJaitnT2ZzVU0zeXBhSDR4RTJYRmcxZnRqVG9kbUdpL1NGejU0eXhZbExMNUtHVExtYW03MlgvUmVkNDdLdW9KdU1jdmgvT09sMWFmc1dxaEgzS0VLcC9DSXVEQlZjRWoiLCJtYWMiOiJlYmMyYmU4ZTg3ZWU1MjI3ZmUwMDdkNWI4MTJiOTQ4NGU1N2Q4YjI0NmI4NGMyNDQxMTA2ZTM4NTk1ZTNkMDBmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 04:57:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImdWSThPc0F2ZmExc2hQYk14Umh0clE9PSIsInZhbHVlIjoiTkYyU3poWmRNb1dDR0pMUURoWDd3ZVdSUVhadFhlYzdGQnMzNFhRNzU0bldTYjVDZm1NOStBc3J0SFJXdTRmVk9QRTZYR2t4V0tWdE1veVFuZHdrQlUyT29FaWo1dXRwK2Y4RUdwUnhUdWVnclJDVlRTbG1maGN6WklvdG9lUXciLCJtYWMiOiIxNjRjNDBlY2Y3YTg0MGQ1MTI2YmM2YzIwYTMwNTQ1YmQ5ODMyYTVjYWY2Y2FkNWEwODFmMGNjZTRkOWZmMmU3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6Ikc1TGI0UGh2RVgwRC9LWGFxd3pGM0E9PSIsInZhbHVlIjoiNlk3YXg0ZHZhYVYrbkN5eUg2blQwS0RYSDc2U0U3ekZJaitnT2ZzVU0zeXBhSDR4RTJYRmcxZnRqVG9kbUdpL1NGejU0eXhZbExMNUtHVExtYW03MlgvUmVkNDdLdW9KdU1jdmgvT09sMWFmc1dxaEgzS0VLcC9DSXVEQlZjRWoiLCJtYWMiOiJlYmMyYmU4ZTg3ZWU1MjI3ZmUwMDdkNWI4MTJiOTQ4NGU1N2Q4YjI0NmI4NGMyNDQxMTA2ZTM4NTk1ZTNkMDBmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 04:57:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848978189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2070577059 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://chri.local/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070577059\", {\"maxDepth\":0})</script>\n"}}