
<li filter-name="<?php echo e($filter->name); ?>"
    filter-type="<?php echo e($filter->type); ?>"
    filter-key="<?php echo e($filter->key); ?>"
	class="nav-item dropdown <?php echo e(Request::get($filter->name)?'active':''); ?>">
    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" data-bs-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><?php echo e($filter->label); ?> <span class="caret"></span></a>
    <div class="dropdown-menu p-0">
      <div class="form-group backpack-filter mb-0">
			<select 
				id="filter_<?php echo e($filter->key); ?>"
				name="filter_<?php echo e($filter->key); ?>"
				class="form-control input-sm select2"
				placeholder="<?php echo e($filter->placeholder); ?>"
				data-filter-key="<?php echo e($filter->key); ?>"
				data-filter-type="select2_multiple"
				data-filter-name="<?php echo e($filter->name); ?>"
				data-language="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>"
				multiple
				>
				<?php if(is_array($filter->values) && count($filter->values)): ?>
					<?php $__currentLoopData = $filter->values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
						<option value="<?php echo e($key); ?>"
							<?php if($filter->isActive() && json_decode($filter->currentValue) && array_search($key, json_decode($filter->currentValue)) !== false): ?>
								selected
							<?php endif; ?>
							>
							<?php echo e($value); ?>

						</option>
					<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
				<?php endif; ?>
			</select>
		</div>
    </div>
  </li>







<?php $__env->startPush('crud_list_styles'); ?>
    
    <?php Basset::basset('https://unpkg.com/select2@4.0.13/dist/css/select2.min.css'); ?>
    <?php Basset::basset('https://unpkg.com/select2-bootstrap-theme@0.1.0-beta.10/dist/select2-bootstrap.min.css'); ?>
    <style>
	  .form-inline .select2-container {
	    display: inline-block;
	  }
	  .select2-drop-active {
	  	border:none;
	  }
	  .select2-container .select2-choices .select2-search-field input, .select2-container .select2-choice, .select2-container .select2-choices {
	  	border: none;
	  }
	  .select2-container-active .select2-choice {
	  	border: none;
	  	box-shadow: none;
	  }
	  .select2-container--bootstrap .select2-dropdown {
	  	margin-top: -2px;
	  	margin-left: -1px;
	  }
	  .select2-container--bootstrap {
	  	position: relative!important;
	  	top: 0px!important;
	  }
    </style>
<?php $__env->stopPush(); ?>





<?php $__env->startPush('crud_list_scripts'); ?>
	
    <?php Basset::basset('https://unpkg.com/select2@4.0.13/dist/js/select2.full.min.js'); ?>
    <?php if(app()->getLocale() !== 'en'): ?>
    <?php Basset::basset('https://unpkg.com/select2@4.0.13/dist/js/i18n/' . str_replace('_', '-', app()->getLocale()) . '.js'); ?>
    <?php endif; ?>

    <script>
        jQuery(document).ready(function($) {
            // trigger select2 for each untriggered select2 box
            $('select[name=filter_<?php echo e($filter->key); ?>]').not('[data-filter-enabled]').each(function () {
            	var filterName = $(this).attr('data-filter-name');
                var filterKey = $(this).attr('data-filter-key');

                $(this).select2({
                	allowClear: true,
					closeOnSelect: false,
					theme: "bootstrap",
					dropdownParent: $(this).parent('.form-group'),
	        	    placeholder: $(this).attr('placeholder'),
                }).on('change', function() {
                    var value = '';
                    if (Array.isArray($(this).val())) {
                        // clean array from undefined, null, "".
                        var values = $(this).val().filter(function(e){ return e === 0 || e });
                        // stringify only if values is not empty. otherwise it will be '[]'.
                        value = values.length ? JSON.stringify(values) : '';
                    }

                    if (!value) {
                        return;
                    }

                    var new_url = updateDatatablesOnFilterChange(filterName, value, true);

                    // mark this filter as active in the navbar-filters
                    if (URI(new_url).hasQuery(filterName, true)) {
                        $("li[filter-key="+filterKey+"]").addClass('active');
                    }

				}).on('select2:unselecting', function(e) {

                    var unselectingValue = e.params.args.data.id;
                    let currentElementValue = $(this).val();

                    if(currentElementValue.length) {

                        currentElementValue = currentElementValue.filter(function(v) {
                            return v !== unselectingValue
                        });

                        if (!currentElementValue.length) {
                            updateDatatablesOnFilterChange(filterName, null, true);

                            $("li[filter-key="+filterKey+"]").removeClass("active");
                            $("li[filter-key="+filterKey+"]").find('.dropdown-menu').removeClass("show");
                        }
                    }

                }).on('select2:clear', function(e) {
                    // when the "x" clear all button is pressed, we update the table
                    updateDatatablesOnFilterChange(filterName, null, true);

                    $("li[filter-key="+filterKey+"]").removeClass("active");
					$("li[filter-key="+filterKey+"]").find('.dropdown-menu').removeClass("show");
                });

				// when the dropdown is opened, autofocus on the select2
				$("li[filter-key="+filterKey+"]").on('shown.bs.dropdown', function () {
					$('#filter_'+filterKey+'').select2('open');
				});

				// clear filter event (used here and by the Remove all filters button)
				$("li[filter-key="+filterKey+"]").on('filter:clear', function(e) {
					$("li[filter-key="+filterKey+"]").removeClass('active');
	                $('#filter_'+filterKey).val(null).trigger('change');
				});
            });
		});
	</script>
<?php $__env->stopPush(); ?>


<?php /**PATH D:\Projects\HRI\crm.hri.com.vn\vendor\backpack\pro/resources/views/filters/select2_multiple.blade.php ENDPATH**/ ?>