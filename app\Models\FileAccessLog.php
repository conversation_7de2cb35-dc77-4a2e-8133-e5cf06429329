<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FileAccessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'file_path',
        'model_type',
        'record_id',
        'field_name',
        'ip_address',
        'user_agent',
        'hash_token',
        'is_successful',
        'error_message',
        'accessed_at'
    ];

    protected $casts = [
        'is_successful' => 'boolean',
        'accessed_at' => 'datetime',
    ];

    /**
     * Relationship với User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope để lấy logs trong khoảng thời gian
     */
    public function scopeInTimeRange($query, $minutes = 10)
    {
        return $query->where('accessed_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Scope để lấy logs theo IP
     */
    public function scopeByIp($query, $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Scope để lấy logs theo user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Ki<PERSON>m tra rate limit cho IP
     */
    public static function checkRateLimit($ip, $minutes = 10, $maxRequests = 100)
    {
        $count = self::byIp($ip)
            ->inTimeRange($minutes)
            ->where('is_successful', true)
            ->count();

        return $count < $maxRequests;
    }

    /**
     * Tạo log mới
     */
    public static function createLog($data)
    {
        return self::create([
            'user_id' => $data['user_id'] ?? auth()->id(),
            'file_path' => $data['file_path'],
            'model_type' => $data['model_type'],
            'record_id' => $data['record_id'],
            'field_name' => $data['field_name'],
            'ip_address' => $data['ip_address'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
            'hash_token' => $data['hash_token'],
            'is_successful' => $data['is_successful'] ?? true,
            'error_message' => $data['error_message'] ?? null,
            'accessed_at' => now()
        ]);
    }
}
