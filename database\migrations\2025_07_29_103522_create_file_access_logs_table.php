<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('file_access_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('ID người dùng truy cập file');
            $table->string('file_path', 500)->comment('Đường dẫn file được truy cập');
            $table->string('model_type', 100)->comment('Tên model chứa file (Cv, ApplyJob, Job, etc.)');
            $table->unsignedBigInteger('record_id')->comment('ID của record chứa file');
            $table->string('field_name', 100)->comment('Tên field chứa file path');
            $table->string('ip_address', 45)->comment('IP address của người truy cập');
            $table->string('user_agent', 500)->nullable()->comment('User agent của browser');
            $table->string('hash_token', 64)->comment('Hash token để bảo mật');
            $table->boolean('is_successful')->default(true)->comment('Truy cập thành công hay không');
            $table->string('error_message', 500)->nullable()->comment('Thông báo lỗi nếu có');
            $table->timestamp('accessed_at')->useCurrent()->comment('Thời gian truy cập');
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'accessed_at']);
            $table->index(['ip_address', 'accessed_at']);
            $table->index(['model_type', 'record_id']);
            $table->index('hash_token');
            
            // Foreign key
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_access_logs');
    }
};
