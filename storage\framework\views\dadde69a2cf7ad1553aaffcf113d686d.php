<?php
    $widget['rel'] = $widget['rel'] ?? 'stylesheet';

    $href = asset($widget['href'] ?? $widget['content'] ?? $widget['path']);
    $attributes = collect($widget)->except(['name', 'section', 'type', 'stack', 'href', 'content', 'path'])->toArray();
?>

<?php $__env->startPush($widget['stack'] ?? 'after_styles'); ?>
    <?php Basset::basset($href, true, $attributes, 'style'); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Projects\HRI\crm.hri.com.vn\vendor\backpack\crud\src\resources\views\ui/widgets/style.blade.php ENDPATH**/ ?>