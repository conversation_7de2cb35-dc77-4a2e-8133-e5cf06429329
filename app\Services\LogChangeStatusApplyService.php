<?php

namespace App\Services;



use App\Models\LogChangeStatusApply;

class LogChangeStatusApplyService
{

    public function queryList(array $filters = [])
    {
        $limit = !empty($filters['limit']) ? $filters['limit'] : 10;
        return LogChangeStatusApply::query()
            ->when(!empty($filters['apply_job_id']), function ($query) use ($filters) {
                $query->where('apply_job_id', $filters['apply_job_id']);
            })->limit($limit)
            ->latest('id');
    }

    public function create(array $data)
    {
        return LogChangeStatusApply::query()->create($data);
    }


}
