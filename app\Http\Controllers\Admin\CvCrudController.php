<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Utils;
use App\Http\Requests\CvRequest;
use App\Jobs\UploadHideCv;
use App\Models\AcademicLevel;
use App\Models\Candidate;
use App\Models\CareerLanguage;
use App\Models\CareerLevel;
use App\Models\Cv;
use App\Models\Skill;
use App\Models\Status;
use App\Models\User;
use App\Services\CvService;
use App\Services\FileServiceS3;
use App\Services\StatusService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * Class CvCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CvCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;


    protected $statusService;


    public function __construct(StatusService $statusService)
    {
        $this->statusService = $statusService;
        parent::__construct();
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Cv::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/cv');
        CRUD::setEntityNameStrings('cv', 'cvs');
        CRUD::denyAccess('delete');
        CRUD::denyAccess('show');
        CRUD::addClause('outSide');
        if (!backpack_user()->can('cv.index')) {
            CRUD::denyAccess('index');
        }
        if (!backpack_user()->can('cv.edit')) {
            CRUD::denyAccess('edit');
        }
        if (!backpack_user()->can('cv.create')) {
            CRUD::denyAccess('create');
        }
        if (!backpack_user()->can('cv.show')) {
            CRUD::denyAccess('show');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::disableResponsiveTable();
        // $this->crud->addClause('roleData');
        $this->filterData();
        CRUD::column('row_number')->type('row_number')->label('#')->orderable(false);
        //        CRUD::setFromDb(); // set columns from db columns.
        $this->crud->column('job_title');
        $this->crud->addColumn([
            // Select
            'label'     => 'Tên ứng viên',
            'type'      => 'select',
            'name'      => 'name', // the db column for the foreign key
            'entity'    => 'candidate', // the method that defines the relationship in your Model
            'attribute' => 'name', // foreign key attribute that is shown to user
            'wrapper'   => [
                'element' => 'a', // the element will default to "a" so you can skip it here
                'href' => function ($crud, $column, $entry, $related_key) {
                    return backpack_url('/candidate/' . $related_key . '/show');
                },
                // 'target' => '_blank',
                // 'class' => 'some-class',
            ],
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhereHas('candidate', function ($q) use ($column, $searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            }
        ]);
        $this->crud->column('email');
        $this->crud->column([
            'name' => 'dob', // The db column name
            'label' => 'Date of birth', // Table column heading
            'type' => 'datetime',
            'format' => 'l ', // use something else than the base.default_datetime_format config value
        ]);
        $this->crud->removeColumn('gender');
        CRUD::column('gender')->label('Gender')->value(function ($item) {
            return $this->statusService->getStatusGender()[$item->gender] ?? '';
        });
        $this->crud->column([
            'label' => 'Trình độ', // Table column heading
            'name' => 'academic_level',
            'type' => 'relationship',
            'entity' => 'academicLevel',
            'attribute' => 'name_vi',
            'model' => AcademicLevel::class,
        ]);
        $this->crud->column([
            'label' => 'Cấp bậc', // Table column heading
            'name' => 'level',
            'type' => 'relationship',
            'entity' => 'careerLevel',
            'attribute' => 'name_vi',
            'model' => CareerLevel::class,
        ]);
        // $this->crud->column('mobile');
        $this->crud->column('university');
        $this->crud->addColumn([
            'name' => 'skills',
            'label' => 'Skills',
            'type' => 'select_multiple',
            'entity' => 'skills',
            'attribute' => 'name',
            'model' => Skill::class,
            'pivot' => true,
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhereHas('skills', function ($q) use ($column, $searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            }
        ]);
        $this->crud->column('created_at')->label('Ngày tạo');
        //        CRUD::column('currency')->label('Currency')->value(function ($item) {
        //            return $this->statusService->getStatusCurrency()[$item->currency] ?? '';
        //        });
        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(CvRequest::class);

        CRUD::addFields($this->fieldData());
        $entry = $this->crud->getCurrentEntry();
        if ($entry && !empty($entry->cv_public) && empty($entry->cv_private)) {
            CRUD::addField([   // view
                'name' => 'btn-gen-private-cv',
                'type' => 'view',
                'view' => 'admins.cvs.elements.btn-gen-private-cv',
                'data' => [
                    'id' => $entry->id,
                ],
                'wrapper'         => [
                    'class' => 'form-group col-md-3',
                ],
            ])->afterField('cv_public');
        }

        $scriptContent = view('vendor.backpack.ui.widgets.js')->render();
        $this->crud->addField([
            'type' => 'custom_html',
            'name' => 'custom_js',
            'value' => $scriptContent,
        ]);
        Widget::add()->type('script')->content('assets/js/admin/cv.js');
        Widget::add()->type('script')->content('assets/js/admin/currency.js');
        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
        CRUD::setEditView('admins.cvs.edit');
        $this->crud->setEditContentClass('col-md-6');
    }

    public function fieldData()
    {
        $candidate = !empty(request('candidate_id')) ? Candidate::findOrFail(request('candidate_id')) : null;
        $candidate_id = !empty(request('candidate_id')) ? request('candidate_id') : null;
        $entry = $this->crud->getCurrentEntry();
        return [
            [
                'type'                 => "relationship",
                'name'                 => 'candidate_id',
                'attribute'            => 'name',             // foreign key attribute that is shown to user
                'ajax'                 => true,
                'placeholder'          => 'Choose option',
                'default'              => $candidate_id,
                'label'                => "Ứng viên",
                'entity'               => 'candidate',        // the method that defines the relationship in your Model
                'model'                => Candidate::class,   // foreign key model
                'minimum_input_length' => 0,
                'wrapper'              => [
                    'class' => 'form-group col-md-3',
                ],
                'inline_create' => [ // specify the entity in singular
                    'entity' => 'candidate', // the entity in singular
                    // OPTIONALS
                    'force_select' => true, // should the inline-created entry be immediately selected?
                    // 'modal_class' => 'modal-dialog modal-xl', // use modal-sm, modal-lg to change width
                    // 'modal_route' => route('candidate-inline-create'), // InlineCreate::getInlineCreateModal()
                    // 'create_route' =>  route('candidate-inline-create-save'), // InlineCreate::storeInlineCreate()
                    'add_button_label' => 'New candidate', // configure the text for the `+ Add` inline button
                    'modal_class' => 'modal-dialog modal-xl modal-dialog-centered', // use modal-sm, modal-lg to change width
                    'include_main_form_fields' => ['name', 'email', 'gender', 'dob', 'university',], // pass certain fields from the main form to the modal, get them with: request('main_form_fields')
                ],
                'validationRules' => 'required',
                // 'inline_create' => true
            ],

            fieldColumnData('name', 'Họ và tên', 'text', 'form-group col-md-3', 'required', $candidate->name ?? null),
            fieldColumnData('job_title', 'Job title', 'text', 'form-group col-md-6', 'required', $candidate->job_title ?? null),
            // fieldColumnData('email', 'Email', 'email', 'form-group col-md-3', 'required', $candidate->email ?? null),
            // fieldColumnData('mobile', 'Điện thoại', 'text', 'form-group col-md-3', 'required', $candidate->mobile ?? null),
            fieldColumnData('dob', 'Ngày sinh', 'date', 'form-group col-md-3', 'required', $candidate->dob ?? null),
            fieldColumnData('can_contact', 'Có thể liên hệ', 'switch', 'form-group col-md-3', ''),
            fieldColumnData('is_open', 'Open work', 'switch', 'form-group col-md-3', ''),
            selectFormArrayData('work_site', 'Địa điểm làm việc', 'form-group col-md-3', $this->statusService->getStatusWorkSize(), ''),
            select2ModelData('Level', 'select2', 'careerLevel', CareerLevel::class, 'name_vi', 'form-group col-md-3', 'required'),
            fieldColumnData('yoe', 'Năm kinh nghiệm', 'number', 'form-group col-md-2', 'required'),
            //            fieldColumnData('salary_current', 'Mức lương hiện tại', 'number', 'form-group col-md-3', ''),
            [
                'name' => 'salary_current',
                'label' => 'Mức lương hiện tại',
                'type'     => 'text',

                'wrapperAttributes' => ['class' => 'form-group col-md-2'],
                'attributes' => ['data-type' => 'currency'],
            ],
            [
                'name' => 'salary_expect',
                'label' => 'Mức lương mong muốn',
                'type'     => 'text',

                'wrapperAttributes' => ['class' => 'form-group col-md-2'],
                'attributes' => ['data-type' => 'currency'],
            ],
            //            fieldColumnData('salary_expect', 'Mức lương mong muốn', 'number', 'form-group col-md-3', ''),
            selectFormArrayData('currency', 'Đơn vị tiền tệ', 'form-group col-md-2', $this->statusService->getStatusCurrency(), ''),
            select2ModelData('Trình độ học vấn', 'select2', 'academicLevel', AcademicLevel::class, 'name_vi', 'form-group col-md-3', 'required'),
            selectFormArrayData('gender', 'Giới tình', 'form-group col-md-3', $this->statusService->getStatusGender(), 'required'),
            select2ModelData('Ngoại ngữ', 'select2_multiple', 'careerLanguages', CareerLanguage::class, 'name_vi', 'form-group col-md-6', 'required'),
            select2ModelData('Skill', 'select2_multiple', 'skills', Skill::class, 'name', 'form-group col-md-12', 'required'),
            fieldColumnData('skill_describe', 'Skill describe', 'text', 'form-group col-md-12', 'max:255'),
            fieldColumnData('linkedin', 'Linkedin', 'text', 'form-group col-md-12', ''),
            fieldColumnData('facebook', 'Facebook', 'text', 'form-group col-md-3', ''),
            fieldColumnData('university', 'University', 'text', 'form-group col-md-3', ''),
            fieldColumnData('old_company', 'Công ty gần nhất', 'text', 'form-group col-md-3', ''),
            [   // Upload
                'name'            => 'cv_public',
                'label'           => 'Cv không che',
                'type'            => 'upload',
                'upload'          => true,
                'path'            => 'cv',
                'disk'            => 's3',
                'wrapper'         => [
                    'class' => 'form-group col-md-' . ($entry && $entry->cv_private ? '12' : '9'),
                ],
                'validationRules' => '',
            ],
            [   // Upload
                'name'            => 'cv_private',
                'label'           => 'Cv che thông tin liên hệ',
                'type'            => 'upload',
                'upload'          => true,
                'path'            => 'cv',
                'disk'            => 's3',
                'wrapper'         => [
                    'class' => 'form-group col-md-12',
                ],
                'validationRules' => '',
            ],
            // inputUpload('cv_public', 'Cv', 'form-group col-md-12', 's3', ''),

            inputUpload('cv_public', 'Cv', 'form-group col-md-12', 's3', ''),
        ];
    }

    public function store()
    {

        $this->crud->hasAccessOrFail('create');

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        // insert item in the db
        $data = $this->crud->getStrippedSaveRequest($request);
        $uploadSuccess = false;
        if (!empty($data['cv_public']) && is_file($data['cv_public'])) {
            $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3($data['cv_public'], PATH_FOLDER_SAVE_CV);
            $uploadSuccess = true;
        }
        if (!empty($data['cv_private']) && is_file($data['cv_private'])) {
            $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3($data['cv_private'], PATH_FOLDER_CV_PRIVATE);
        }
        $data['salary_current'] = str_replace(',', '', $data['salary_current']);
        $data['salary_expect'] = str_replace(',', '', $data['salary_expect']);
        $data['salary_current'] = explode('.', $data['salary_current'])[0];
        $data['salary_expect'] = explode('.', $data['salary_expect'])[0];
        $item = $this->crud->create($data);
        if ($uploadSuccess) dispatch(new UploadHideCv($item));
        $this->data['entry'] = $this->crud->entry = $item;

        // show a success message
        \Alert::success(trans('backpack::crud.insert_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    public function update()
    {
        $this->crud->hasAccessOrFail('update');

        // execute the FormRequest authorization and validation, if one is required
        $request = $this->crud->validateRequest();

        // register any Model Events defined on fields
        $this->crud->registerFieldEvents();

        $data = $this->crud->getStrippedSaveRequest($request);
        $uploadSuccess = false;

        if (!empty($data['cv_public']) && is_file($data['cv_public'])) {
            // $extension = pathinfo($data['cv_public'], PATHINFO_EXTENSION);
            // $target_path = PATH_FOLDER_SAVE_CV . '/' . date('Y') . '/' . date('m') . '/' . date('d') . '/' . md5($data['cv_public']) . '.' . $extension;
            $target_path = PATH_FOLDER_SAVE_CV;
            $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3($data['cv_public'], $target_path);
            $uploadSuccess = true;
        }
        if (!empty($data['cv_private']) && is_file($data['cv_private'])) {
            // $extension = pathinfo($data['cv_private'], PATHINFO_EXTENSION);
            // $target_path = PATH_FOLDER_SAVE_CV . '/' . date('Y') . '/' . date('m') . '/' . date('d') . '/' . md5($data['cv_private']) . '.' . $extension;
            $target_path = PATH_FOLDER_CV_PRIVATE;
            $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3($data['cv_private'], $target_path);
            // $uploadSuccess = true;
        }
        // update the row in the db
        // dd($data);
        $data['salary_current'] = str_replace(',', '', $data['salary_current']);
        $data['salary_expect'] = str_replace(',', '', $data['salary_expect']);
        $data['salary_current'] = explode('.', $data['salary_current'])[0];
        $data['salary_expect'] = explode('.', $data['salary_expect'])[0];
        $item = $this->crud->update($request->get($this->crud->model->getKeyName()), $data);
        if ($uploadSuccess) dispatch(new UploadHideCv($item));
        $this->data['entry'] = $this->crud->entry = $item;

        // show a success message
        \Alert::success(trans('backpack::crud.update_success'))->flash();

        // save the redirect choice for next time
        $this->crud->setSaveAction();

        return $this->crud->performSaveAction($item->getKey());
    }

    public function ajaxSearch(Request $request)
    {
        $data = $request->all();
        $data['limit'] = 20;
        return response()->json((new CvService())->queryList($data)->selectRaw('id,job_title as name')->get());
    }

    public function fetchCandidate(Request $request)
    {
        $q = $request->get('q', '');
        if (!empty($q)) {
            $candidates = Candidate::where('name', 'like', '%' . $q . '%')->orWhere('email', $q)->orWhere('mobile', $q);
            $candidates = $candidates->paginate(20);
            $candidates->getCollection()->transform(function ($candidate) {
                return [
                    'id' => $candidate->id,
                    'name' => $candidate->name . ' - ' . $candidate->email . ' - ' . Utils::maskPhoneNumber($candidate->mobile),
                ];
            });
            return response()->json($candidates);
        }
        return response()->json([]);
    }

    public function filterData()
    {
        $years = [];
        for ($i = 1969; $i < date('Y'); $i++) {
            $years[$i] = $i;
        }
        $this->crud->addFilter(
            [
                'name' => 'email',
                'type' => 'text',
                'label' => 'Email'
            ],
            false,
            function ($value) {
                $this->crud->query->whereHas('candidate', function ($query) use ($value) {
                    $query->where('email', 'like', '%' . $value . '%');
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'mobile',
                'type' => 'text',
                'label' => 'Mobile'
            ],
            false,
            function ($value) {
                $this->crud->query->whereHas('candidate', function ($query) use ($value) {
                    $query->where('mobile', 'like', '%' . $value . '%');
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'from_year',
                'type' => 'select2',
                'label' => 'Từ năm sinh'
            ],
            function () use ($years) {
                return $years;
            },
            function ($value) {
                $this->crud->addClause('where', 'dob', '>=', $value . '-01-01');
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'to_year',
                'type' => 'select2',
                'label' => 'Đến năm sinh'
            ],
            function () use ($years) {
                return $years;
            },
            function ($value) {
                $this->crud->addClause('where', 'dob', '<=', $value . '-12-31');
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'skill',
                'type' => 'select2_multiple',
                'label' => 'Skill'

            ],
            function () {
                return Skill::all()->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('skills', function ($query) use ($value) {
                    $query->whereIn('skill_id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'language',
                'type' => 'select2_multiple',
                'label' => 'Language'
            ],
            function () {
                return CareerLanguage::all()->pluck('slug', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('careerLanguages', function ($query) use ($value) {
                    $query->whereIn('career_language_id', json_decode($value));
                });
            }
        );

        $yoe = [];
        for ($i = 1; $i <= 10; $i++) {
            $yoe[$i] = $i;
        }
        $this->crud->addFilter(
            [
                'name' => 'yoe',
                'type' => 'select2_multiple',
                'label' => 'Yoe'
            ],
            function () use ($yoe) {
                return $yoe;
            },
            function ($value) {
                if (in_array(10, json_decode($value))) {
                    $this->crud->addClause('where', 'yoe', '>=', 10);
                } else {
                    $this->crud->addClause('orWhere', 'yoe', '=', json_decode($value));
                }
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'level',
                'type' => 'select2_multiple',
                'label' => 'Level'
            ],
            function () {
                return CareerLevel::all()->pluck('slug', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('careerLevel', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );


        $this->crud->addFilter(
            [
                'name' => 'work_site',
                'type' => 'select2_multiple',
                'label' => 'Work site'
            ],
            function () {
                return  Status::where('group', 'work-site')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('statusWorkSite', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'gender',
                'type' => 'select2_multiple',
                'label' => 'Gender'
            ],
            function () {
                return  Status::where('group', 'gender')->pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->query->whereHas('statusGender', function ($query) use ($value) {
                    $query->whereIn('id', json_decode($value));
                });
            }
        );

        // $this->crud->addFilter(
        //     [
        //         'name' => 'mobile',
        //         'type' => 'text',
        //         'label' => 'Mobile'
        //     ],
        //     false,
        //     function ($value) {
        //         $this->crud->query->where('mobile', 'like', '%' . $value . '%');
        //     }
        // );

        // $this->crud->addFilter(
        //     [
        //         'name' => 'candidate',
        //         'type' => 'select2_multiple',
        //         'label' => 'Candidate'
        //     ],
        //     function () {
        //         return Candidate::pluck('name', 'id')->toArray();
        //     },
        //     function ($value) {
        //         $this->crud->query->whereHas('candidate', function ($query) use ($value) {
        //             $query->whereIn('id', json_decode($value));
        //         });
        //     }
        // );

        $this->crud->addFilter([
            'name' => 'createdBy',
            'type' => 'select2_multiple',
            'label' => 'Người thực hiện',
        ], function () {
            return User::pluck('email', 'id')->map(function ($email) {
                return Utils::getUsernameFromEmail($email);
            })->all();
        }, function ($value) {
            $this->crud->query->whereHas('createdBy', function ($query) use ($value) {
                $query->whereIn('id', json_decode($value));
            });
        });

        CRUD::filter('from_to')
            ->type('date_range')
            ->label('Thời gian cập nhật cuối')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                CRUD::addClause('where', 'updated_at', '>=', $dates->from);
                CRUD::addClause('where', 'updated_at', '<=', $dates->to . ' 23:59:59');
            });
        $this->crud->addFilter(
            [
                'name' => 'date_range',
                'type' => 'date_range',
                'label' => 'Khoảng thời gian'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                $this->crud->addClause('where', 'created_at', '>=', $dates->from);
                $this->crud->addClause('where', 'created_at', '<=', $dates->to . ' 23:59:59');
            }
        );
    }
}
