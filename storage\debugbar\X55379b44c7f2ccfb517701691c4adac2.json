{"__meta": {"id": "X55379b44c7f2ccfb517701691c4adac2", "datetime": "2025-07-29 10:49:30", "utime": **********.077257, "method": "GET", "uri": "/admin/secure-file/download?id=115121&model=Cv&field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753760969.650977, "end": **********.077271, "duration": 0.42629408836364746, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1753760969.650977, "relative_start": 0, "end": 1753760969.961418, "relative_end": 1753760969.961418, "duration": 0.3104410171508789, "duration_str": "310ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753760969.961429, "relative_start": 0.3104522228240967, "end": **********.077272, "relative_end": 9.5367431640625e-07, "duration": 0.11584281921386719, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31640192, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/secure-file/download", "middleware": "web, App\\Http\\Middleware\\CheckOutsideAccess, admin", "controller": "App\\Http\\Controllers\\FileDownloadController@download", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "where": [], "as": "secure-file.download", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FHttp%2FControllers%2FFileDownloadController.php&line=25\" onclick=\"\">app/Http/Controllers/FileDownloadController.php:25-151</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02175, "accumulated_duration_str": "21.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}], "start": **********.007537, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:189", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=189", "ajax": false, "filename": "EloquentUserProvider.php", "line": "189"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/helpers.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\backpack\\crud\\src\\helpers.php", "line": 212}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 983}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 815}], "start": **********.019582, "duration": 0.020579999999999998, "duration_str": "20.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "c_hri", "explain": null, "start_percent": 0, "width_percent": 94.621}, {"sql": "select count(*) as aggregate from `file_access_logs` where `ip_address` = '127.0.0.1' and `accessed_at` >= '2025-07-29 10:39:30' and `is_successful` = 1", "type": "query", "params": [], "bindings": ["127.0.0.1", "2025-07-29 10:39:30", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Services/FileSecurityService.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Services\\FileSecurityService.php", "line": 21}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.063616, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:71", "source": {"index": 16, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=71", "ajax": false, "filename": "FileAccessLog.php", "line": "71"}, "connection": "c_hri", "explain": null, "start_percent": 94.621, "width_percent": 2.207}, {"sql": "insert into `file_access_logs` (`user_id`, `file_path`, `model_type`, `record_id`, `field_name`, `ip_address`, `user_agent`, `hash_token`, `is_successful`, `error_message`, `accessed_at`, `updated_at`, `created_at`) values (null, 'INVALID_HASH', 'Cv', '115121', 'cv_private', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61', 0, 'Invalid hash token', '2025-07-29 10:49:30', '2025-07-29 10:49:30', '2025-07-29 10:49:30')", "type": "query", "params": [], "bindings": [null, "INVALID_HASH", "Cv", "115121", "cv_private", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61", 0, "Invalid hash token", "2025-07-29 10:49:30", "2025-07-29 10:49:30", "2025-07-29 10:49:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FileDownloadController.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Http\\Controllers\\FileDownloadController.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.065823, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "FileAccessLog.php:81", "source": {"index": 21, "namespace": null, "name": "app/Models/FileAccessLog.php", "file": "D:\\Projects\\HRI\\crm.hri.com.vn\\app\\Models\\FileAccessLog.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FFileAccessLog.php&line=81", "ajax": false, "filename": "FileAccessLog.php", "line": "81"}, "connection": "c_hri", "explain": null, "start_percent": 96.828, "width_percent": 3.172}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2Fcrm.hri.com.vn%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw", "_previous": "array:1 [\n  \"url\" => \"http://chri.local/admin/secure-file/download?field=cv_private&hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61&id=115121&model=Cv\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_backpack": "$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi"}, "request": {"path_info": "/admin/secure-file/download", "status_code": "<pre class=sf-dump id=sf-dump-1272292348 data-indent-pad=\"  \"><span class=sf-dump-num>403</span>\n</pre><script>Sfdump(\"sf-dump-1272292348\", {\"maxDepth\":0})</script>\n", "status_text": "Forbidden", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-873836611 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115121</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Cv</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cv_private</span>\"\n  \"<span class=sf-dump-key>hash</span>\" => \"<span class=sf-dump-str title=\"64 characters\">4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873836611\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-95773578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-95773578\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2002178541 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">chri.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1265 characters\">remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkN1dGEvelkxWkk0OVBUeTV6ZkhKMGc9PSIsInZhbHVlIjoidW4zTC9ZaUVueXVkSWhGL1BWMkJqTk04ZHI1ZWdndmdiWGhiT0dGZU16bHNUbk5ybmdPeXFqU0hKY0cxSFR3S3d2RmM5VzRyRGtXQjlLZWtoMzFQZnk5ZUZ1WjdhOUU5SFpPYlBjUGxiWU1uV09FMnZrYWQyclFlOThTTFdLdUVhK3RHU2o4OUJIektIb0JUMkFPNlJUbXJSQXVpZ01TZWFPT0tvRHprNG8vRmI4blFzLzdlUVZWZ2dqSkZHQVlBSTdxeXBEL3h2R3IvTEZmTGZOVjFhK1pybGVCRVdkeWM2cEdkdVJUNnVqST0iLCJtYWMiOiI0MzIzMDZkYjU0YjQ3ZTNiNmI4NmFlMjMxNDI4ZTk5MzkwYjUzOTJiMmEzYTE4NTRmMDc4ODcwYjc3MjRiZDg1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilo3ZWpmL0QvMnYydi9MTEVHUjhORnc9PSIsInZhbHVlIjoiRkF0RnJSNGFWTTdGcEx2WHFFdE5zemRnOGpmY3l6SDVLOHhRa09BTnBGNUZYcnhyM3FpZmt0bHB4aGp5TTBzNkE1TUJjR0lIS0RoMFNjVW1ya08zQ0djcVNtOTRYSFVMMnZSSmpyUHJlM2EycGQ0c1JLLzIyVjVQb0xTODY3TWkiLCJtYWMiOiJkODg4MDhjMTNmMWIzODQ2NTBmMTU2NzZiMjA3ZDk1NGRiMDg3ZDMxOTBkNDE2ZTc1ODI4ZDljNWQyZjk5MDc5IiwidGFnIjoiIn0%3D; hri_crm_2023_session=eyJpdiI6IkRLSXM3MGhLZmdjYmxpSTcxN1BSV1E9PSIsInZhbHVlIjoieE9sc0NrRFFxdjBVLzI1c0h6SjJldXdHSWpySmt0blNMdHF4VTBXM045ZUVOT0ZRcnduNUFXRWhXSExOMDdOWGpYd1VmYzdNRTZ6MHMrNzMwSHFKTzgvVFBOYVAvY1NlKzA5K0FITDgwZFFXMmlKcUdFRHFCRytJN1M3QkRCWlIiLCJtYWMiOiI1MWExYWE5ZTA3NDkzMzFjOTVlOGM4YmVjNGE5OTllZDYxZThlNjc5M2YzODYzZjA0YmQyMWM3NWZiNjI0OGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002178541\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-688735102 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|HIRHDTqi5hIlRQVzefpe84qHTYi6P0AYF1RBEqHTQC6EIo3L6VXUeWAwVyHE|$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>hri_crm_2023_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8P7PNeDAQ4lcZ8LxZzaIqTyLfTc8siVr7DQh2E3M</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688735102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1996469165 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 03:49:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InZMUFdueFFxeHdFUmsvRzlxajNXR2c9PSIsInZhbHVlIjoiaUV3RjhCQjRpK1haZEkyTXVnenhpYjF6QTdUWG50R04yRzFseEtYczk1cHhxQkMzL3VWTUJtc1ZMOUJ0cDh2eTJHa3QrQ0hUOGZsTVhIUWU2SWVuMG9OVGl0eGlocXpYSU9vMEVuY2VSdkRHMkRiNU1GNHZYd3JFMXNXczV4b0EiLCJtYWMiOiJhYWZlMWI3NzAzN2FhYTAzNGJkZmRhMTdhYmY3MTQ2OGJhYmQ1ZTVkYzg1MTZiNjU4ZThjOGUwZGMzZjNiNjdmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">hri_crm_2023_session=eyJpdiI6IktmK3VzWm5zMTNxdklYWU0rdUw4K1E9PSIsInZhbHVlIjoiWm9BNzVuS1VCbk1OU1dzMkJ2UjFQcE1SUlhiblpSNnRoaUN2bk11YkhIM1RpZEV1dXpBVFJNdzNNSmNKZlJ4QTUrTCtLcUNOMjJJR2lLRlkwbVF0WHNQUkJpbUk4byt6MHM3UDVjZjE2bm9KbW03MWIzTW4yQ0VIMWtZWjZ5VlEiLCJtYWMiOiJlYmM1Y2VhZTRjZGM4ZTExZjA5NzRhODcxODkxODJhZDViM2UwZGNhMzQ0MDU4Yjc5Y2ZhZTZlMjZlMTFlOGMzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 05:49:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InZMUFdueFFxeHdFUmsvRzlxajNXR2c9PSIsInZhbHVlIjoiaUV3RjhCQjRpK1haZEkyTXVnenhpYjF6QTdUWG50R04yRzFseEtYczk1cHhxQkMzL3VWTUJtc1ZMOUJ0cDh2eTJHa3QrQ0hUOGZsTVhIUWU2SWVuMG9OVGl0eGlocXpYSU9vMEVuY2VSdkRHMkRiNU1GNHZYd3JFMXNXczV4b0EiLCJtYWMiOiJhYWZlMWI3NzAzN2FhYTAzNGJkZmRhMTdhYmY3MTQ2OGJhYmQ1ZTVkYzg1MTZiNjU4ZThjOGUwZGMzZjNiNjdmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">hri_crm_2023_session=eyJpdiI6IktmK3VzWm5zMTNxdklYWU0rdUw4K1E9PSIsInZhbHVlIjoiWm9BNzVuS1VCbk1OU1dzMkJ2UjFQcE1SUlhiblpSNnRoaUN2bk11YkhIM1RpZEV1dXpBVFJNdzNNSmNKZlJ4QTUrTCtLcUNOMjJJR2lLRlkwbVF0WHNQUkJpbUk4byt6MHM3UDVjZjE2bm9KbW03MWIzTW4yQ0VIMWtZWjZ5VlEiLCJtYWMiOiJlYmM1Y2VhZTRjZGM4ZTExZjA5NzRhODcxODkxODJhZDViM2UwZGNhMzQ0MDU4Yjc5Y2ZhZTZlMjZlMTFlOGMzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 05:49:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996469165\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-442171812 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7Xx53uZItzl13nEbAd2QB3p4ysHjBOWqUjLyQhw</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"150 characters\">http://chri.local/admin/secure-file/download?field=cv_private&amp;hash=4d12821fb22ee50b566b2c47cc4e113437fd63f9426634d0f968138ecbbbad61&amp;id=115121&amp;model=Cv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_backpack_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_backpack</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$1tVvGqPgyc.SSJEIsXt3F.7J93ktLKanq3HrCt3Tb.QTNpkIREUOi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442171812\", {\"maxDepth\":0})</script>\n"}}